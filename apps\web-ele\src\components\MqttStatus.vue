<template>
  <div class="mqtt-status">
    <el-badge
      :value="status.isConnected ? '已连接' : '未连接'"
      :type="status.isConnected ? 'success' : 'danger'"
      class="mqtt-badge"
    >
      <el-button
        :icon="status.isConnected ? 'Connection' : 'Close'"
        size="small"
        :type="status.isConnected ? 'success' : 'danger'"
        @click="handleReconnect"
        :loading="reconnecting"
      >
        MQTT
      </el-button>
    </el-badge>

    <el-tooltip v-if="!status.isConnected && status.reconnectAttempts > 0">
      <template #content>
        重连尝试次数: {{ status.reconnectAttempts }}/{{ status.maxReconnectAttempts }}
      </template>
      <el-text type="warning" size="small">
        ({{ status.reconnectAttempts }}/{{ status.maxReconnectAttempts }})
      </el-text>
    </el-tooltip>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { usePrintStore } from '#/stores/printStore';
import { ElMessage } from 'element-plus';

const printStore = usePrintStore();
const status = ref({
  isConnected: false,
  reconnectAttempts: 0,
  maxReconnectAttempts: 5,
  config: null
});

const reconnecting = ref(false);
let statusTimer: NodeJS.Timeout | null = null;

// 更新状态
const updateStatus = () => {
  status.value = printStore.getMqttStatus();
};

// 手动重连
const handleReconnect = async () => {
  if (reconnecting.value) return;

  reconnecting.value = true;
  try {
    const success = await printStore.reconnectMqtt();
    if (success) {
      ElMessage.success('MQTT重连成功');
    } else {
      ElMessage.error('MQTT重连失败');
    }
  } catch (error) {
    ElMessage.error('MQTT重连失败');
  } finally {
    reconnecting.value = false;
  }
};

onMounted(() => {
  updateStatus();
  // 每5秒更新一次状态
  statusTimer = setInterval(updateStatus, 5000);
});

onUnmounted(() => {
  if (statusTimer) {
    clearInterval(statusTimer);
  }
});
</script>

<style scoped>
.mqtt-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.mqtt-badge {
  display: inline-flex;
  align-items: center;
}
</style>
