<template>
  <div class="page-container">
    <div class="main-content-row">
      <el-row :gutter="6" class="h-full">
        <el-col :span="5" class="h-full"><OrderCardList /></el-col>
        <el-col :span="14" class="h-full"><OrderDisplayPage /></el-col>
        <el-col :span="5" class="h-full"><PrinterManagement /></el-col>
      </el-row>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, onUnmounted } from 'vue';
import { usePrintStore } from '#/stores/printStore';
import OrderCardList from './components/OrderCardList.vue';
import OrderDisplayPage from './components/OrderDisplayPage.vue';
import PrinterManagement from './components/PrinterManagement.vue';
import { ElRow, ElCol } from 'element-plus';

// 初始化Store
const printStore = usePrintStore();

// 组件挂载时的初始化
onMounted(async () => {
  // 设置外部监听器
  printStore.setupExternalListener();
});

// 组件卸载时清理
onUnmounted(() => {
  console.log('打印机页面卸载');
});
</script>

<style lang="scss" scoped>
.page-container { height: 96vh; background-color: #111827; padding-top: 4px;}
.main-content-row, .h-full { height: 96vh; padding-top: 4px;}
</style>


