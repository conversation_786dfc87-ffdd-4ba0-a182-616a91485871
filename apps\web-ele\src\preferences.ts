import { defineOverridesPreferences } from '@vben/preferences';

/**
 * @description 项目配置文件
 * 只需要覆盖项目中的一部分配置，不需要的配置不用覆盖，会自动使用默认配置
 * !!! 更改配置后请清空缓存，否则可能不生效
 */
export const overridesPreferences = {
  "app": {
    "layout": "header-nav",
    "name": "飞龙云印",
    "dynamicTitle": false,
    "enableCheckUpdates": false
  },
  "copyright": {
    "companyName": "飞龙云印",
    "companySiteLink": "飞龙云印",
    "date": "2025"
  },
  "footer": {
    "enable": false
  },
  "tabbar": {
    "enable": false
  },
  "theme": {
    "builtinType": "gray",
    "colorPrimary": "hsl(240 5.9% 10%)",
    "mode": "light",
    "semiDarkHeader": true
  },
  "logo": {
    "enable": true,
    "fit": 'contain',
    "source": '/logo.png',
  },
  "widget": {
    "fullscreen": false,
    "globalSearch": false,
    "languageToggle": false,
    "lockScreen": false,
    "notification": false,
    "refresh": true,
    "sidebarToggle": false,
    "themeToggle": false
  }
}
