// 打印API 参数
export namespace PrinterApi {
  export interface GetReadyPrintFilesParams {
    pageNum: number;
    pageSize: number;
    shopId: string;
    queryType: number;
    // 新增历史订单搜索参数
    startDate?: string | Date;
    endDate?: string | Date;
    status?: string;
    bussId?: string;
  }

  export interface ConfirmPrintFileParams {
    printFileId: string;
    printerId: string;
  }

  export interface SetPrinterSettingsParams {
    printerId: string;
    params: any;
  }

  export interface UpdatePrinterStatusParams {
    printerId: string;
    status: any;
  }
  export interface getShopPrinterParams {
    shopId: string;
  }
}

/**
 * 打印机配置能力的接口
 */
export interface PrinterCapability {
  id: string;
  printerName: string;
  alias: string;
  paperSizes: ('A3' | 'A4' | 'B4' | 'B5')[];
  paperBins: { [paperSize: string]: string }; // 纸张对应的纸盒配置
  duplexModes: ('SINGLE' | 'DOUBLE')[];
  colorModes: ('BLACKWHITE' | 'COLOR')[];
  layouts: ('VERTICAL' | 'HORIZONTAL')[];
  supportedTypes: ('1' | '2' | '3' | '4')[];
  orderPageBin: string; // 订单页专用纸盒
  status: 'enabled' | 'disabled';
}

/**
 * 全局打印设置
 */
export interface GlobalPrintSettings {
  mode: 'auto' | 'manual';
  sound: 'play' | 'mute';
  orderPageMode: 'byCopies' | 'byPrinter';
}

/**
 * 从API获取的原始订单数据结构
 */
export interface ApiOrder {
  bussId: string;
  orderType: '1' | '2';
  payAmount: number;
  printFileList: ApiFile[];
  updateTime: string;
  status: '1' | '2' | '3' | '4' | '5' | '6'; // 1-待打印，2-已确认(自动)，3-已确认(人工)，4-打印中，5-已打印，6-打印失败
}

/**
 * 从API获取的原始文件数据结构
 */
export interface ApiFile {
  originFileName: string;
  fileUrl: string;
  fileType: '1' | '2' | '3' | '4'; // 1:文档, 2:封面, 3:照片, 4:订单封面
  printTimes: number;
  paperSize: 'A4' | 'A3' | 'B5';
  printPage: 'SINGLE' | 'DOUBLE';
  printColor: 'BLACKWHITE' | 'COLOR';
  paperKind: 'UPSCALE' | 'NORMAL';
  status: '0' | '1' | '2';  // 0: 未打印 1: 打印成功 2: 打印失败
}

/**
 * 打印文件的列表
 */
export interface PrintFile {
  id: number;
  fileId: string;
  fileKey: string;
  fileName: string;
  copies: number;
  size: 'A4' | 'A3' | 'B5';
  range: string;
  duplex: '单面' | '双面';
  color: '黑白' | '经济彩印' | '标准彩印';
  paper: '普通' | '高光';
  status: '0' | '1' | '2' | '3';  // 0: 未打印 1: 打印成功 2: 打印失败 3: 打印中
  fileUrl: string;
  fileType: '1' | '2' | '3' | '4';
}


// 定义打印队列中每个任务的数据结构
export interface PrintJob {
  jobKey: string; // 使用 fileKey 作为唯一标识
  fileId: string;
  file: PrintFile;
  status: 'queuing' | 'failed' | 'completed'; // 队列中的状态
  printerName: string;
  timestamp: string;
  errorMessage?: string;
}

/**
 * 打印机的数据结构
 */
export interface Printer {
  id: number;
  name: string;
  serial: string;
  status: 'disabled' | 'idle' | 'printing';
  outputType: string;
  specs: string[];
  driver: string;
}

/**
 * 预览组件所需的数据结构
 */
export interface PreviewData {
  type: 'pdf' | 'image';
  url: string;
}




