<template>
  <div class="feature-rich-previewer">
    <div  class="toolbar">
      <div class="tool-group">
        <el-button :icon="ArrowLeft" size="small" :disabled="pdfPage <= 1" @click="changePage(-1)" />
        <div v-if="pdfPageCount > 0" class="page-indicator">
          <el-input-number
            v-model="pdfPage"
            size="small"
            :min="1"
            :max="pdfPageCount"
            controls-position="right"
            class="page-input"
          />
          <span class="page-count">/ {{ pdfPageCount }}</span>
        </div>
        <el-button :icon="ArrowRight" size="small" :disabled="pdfPage >= pdfPageCount" @click="changePage(1)" />
      </div>
      <div class="tool-group">
        <el-button :icon="ZoomOut" size="small" @click="changeZoom(-0.2)" />
        <span class="scale-indicator">{{ (scale * 100).toFixed(0) }}%</span>
        <el-button :icon="ZoomIn" size="small" @click="changeZoom(0.2)" />
      </div>
      <div class="tool-group">
        <el-button :icon="RefreshLeft" size="small" @click="changeRotation(-90)">旋转</el-button>
        <el-button :icon="FullScreen" size="small" @click="fitPage()">适应页面</el-button>
      </div>
    </div>
    <div class="preview-content-area" v-loading="pdfLoading" element-loading-background="rgba(45, 55, 72, 0.8)">
      <div v-if="!printStore.activePreviewFile" class="no-preview">
        <el-empty description="请在下方列表中选择一个文件进行预览" />
      </div>
      <template v-else>
        <vue-pdf-embed
          ref="pdfEmbedRef"
          :key="pdfRenderKey"
          :source="printStore.activePreviewFile.url"
          :page="pdfPage"
          :rotation="rotation"
          :scale="scale"
          @loaded="handlePdfLoaded"
          @loading-failed="handlePdfError"
        />
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from 'vue';
import { ElEmpty, ElButton, ElInputNumber, ElIcon } from 'element-plus';
import { ArrowLeft, ArrowRight, ZoomIn, ZoomOut, RefreshLeft, FullScreen } from '@element-plus/icons-vue';
import VuePdfEmbed from 'vue-pdf-embed';
import { usePrintStore } from '#/stores/printStore';

const printStore = usePrintStore();
const pdfEmbedRef = ref<any>(null);

// --- 本地UI控制状态 ---
const pdfRenderKey = ref(0);
const pdfPage = ref(1);
const pdfPageCount = ref(0);
const pdfLoading = ref(false);
const scale = ref(1);
const rotation = ref(0);

// [关键修改 1] 新增一个标志位，用于判断是否是新文件的首次加载
const isInitialLoad = ref(true);

// --- 方法 ---

// handlePdfLoaded 现在会检查标志位
const handlePdfLoaded = (pdfProxy: any) => {
  pdfLoading.value = false;
  pdfPageCount.value = pdfProxy.numPages;

  // 只在首次加载新文件时，才自动调用 fitPage
  if (isInitialLoad.value) {
    nextTick(() => {
      fitPage(pdfProxy);
    });
    // 调用后立刻将标志位设为 false，防止后续的缩放操作再次触发
    isInitialLoad.value = false;
  }
};

const handlePdfError = (error: any) => {console.log(error);};
function forcePdfRerender() { pdfRenderKey.value += 1; }
function changePage(delta: number) {
  const newPage = pdfPage.value + delta;
  if (newPage > 0 && newPage <= pdfPageCount.value) {
    pdfPage.value = newPage;
  }
}


function changeZoom(delta: number) {
  scale.value = Math.max(0.1, scale.value + delta);
  forcePdfRerender();
}

function changeRotation(delta: number) {
  rotation.value += delta;
  forcePdfRerender();
}

// fitPage 函数现在可以被用户手动点击，也可以被首次加载时调用
const fitPage = async (pdf?: any) => {
  const pdfProxy = pdf || pdfEmbedRef.value?.pdf;
  const container = pdfEmbedRef.value?.$el?.parentElement;
  if (!pdfProxy || !container || container.clientWidth === 0) return;

  const page = await pdfProxy.getPage(1);
  const viewport = page.getViewport({ scale: 1 });
  const newScale = 1 + Math.min(container.clientWidth / viewport.width, container.clientHeight / viewport.height) * 0.98;

  scale.value = newScale;
  forcePdfRerender();
};

// watch 在切换文件时，负责重置 isInitialLoad 标志位
watch(() => printStore.activePreviewFile, (newFile, oldFile) => {
  if (!newFile || !newFile.url || newFile.url === oldFile?.url) {
    if (!newFile) pdfLoading.value = false;
    return;
  }

    isInitialLoad.value = true; // <-- 切换新文件，重置标志位为 true
    pdfPage.value = 1;
    pdfPageCount.value = 0;
    rotation.value = 0;
    // scale.value = 1; // 临时设置为1，等待 fitPage 计算精确值
    pdfLoading.value = true;
    forcePdfRerender();
}, { immediate: true });
</script>

<style scoped lang="scss">
/* 所有样式保持不变 */
.feature-rich-previewer { height: 100%; display: flex; flex-direction: column; background-color: #2d3748; border: 1px solid #4a5568; border-radius: 6px; overflow: hidden; }
.toolbar { flex-shrink: 0; display: flex; justify-content: center; align-items: center; gap: 16px; padding: 8px; background-color: #1f2937; border-bottom: 1px solid #4a5568; color: #a0aec0; }
.tool-group { display: flex; align-items: center; gap: 8px; }
.page-indicator { display: flex; align-items: center; .page-input { width: 70px; :deep(.el-input__inner) { text-align: right; } } .page-count { margin-left: 4px; min-width: 40px; } }
.scale-indicator { min-width: 50px; text-align: center; }
.preview-content-area { flex-grow: 1; position: relative; overflow: auto; display: flex; justify-content: center; }
.no-preview { display: flex; align-items: center; justify-content: center; height: 100%; }
:deep(.vue-pdf-embed) { display: inline-block; & > div { margin-bottom: 8px; box-shadow: 0 2px 8px rgba(0,0,0,.15); } canvas { width: auto !important; height: auto !important; max-width: 100%; } }
.image-preview-wrapper { width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; }
.preview-image { max-width: 100%; max-height: 100%; object-fit: contain; }
</style>
