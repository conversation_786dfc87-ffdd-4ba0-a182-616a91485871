// --- aardio 交互的统一接口 ------------------------------------------------------------------------------------------------------------------
const aardio = {

  // 下载文件
  downloadFile : async (options) => {
    await window.aardio.downloadFile();
  },


  // 选择文件
  selectFile : async () => {
    return await window.aardio.selectFile();
  },

  // 数据库操作
  managePrinters: async (action , payload = {}) => {
    console.log('正在调用 aardio managePrinters, 参数:', payload);
    let res = await window.aardio.managePrinters(action ,JSON.stringify(payload));
    return res;
  },

  /**
   * 获取打印机列表
   * @returns Promise<AardioPrinter[]>
   */
  getPrinters: async () => {
    const printerJson = await window.aardio.getPrinters();
    const printerList = JSON.parse(printerJson);
    console.log(printerList);
    return printerList;
  },

  /**
   * 调用 aardio 执行打印
   * @param options 打印参数
   */
  printPdf: async (options) => {
    console.log('正在调用 aardio printPdf, 参数:', options);
    await window.aardio.printPdf(options);
  },

  /**
   * 获取机器码
   * @returns Promise<string>
   */
  getMachineCode: async () => {
    const code = await window.aardio.getMachineCode();
    console.log('获取到的机器码:', code);
    return code;
  },

};
export { aardio };

