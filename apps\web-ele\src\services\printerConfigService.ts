import type { PrinterCapability } from '../types/print.types';

const STORAGE_KEY = 'vben_printer_configurations';

/**
 * 从 LocalStorage 读取所有打印机配置
 */
export function getStoredPrinterConfigs(): PrinterCapability[] {
  const data = localStorage.getItem(STORAGE_KEY);
  try {
    return data ? JSON.parse(data) : [];
  } catch (error) {
    console.error("解析本地存储的打印机配置失败", error);
    return [];
  }
}

/**
 * 将所有打印机配置保存到 LocalStorage
 * @param configs - 最新的配置数组
 */
export function savePrinterConfigs(configs: PrinterCapability[]): void {
  localStorage.setItem(STORAGE_KEY, JSON.stringify(configs));
}
