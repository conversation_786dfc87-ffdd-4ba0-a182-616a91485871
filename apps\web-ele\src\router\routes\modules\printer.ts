import type { RouteRecordRaw } from 'vue-router';
import { BasicLayout } from '#/layouts';
import { $t } from '#/locales';
export default [
  {
    component:BasicLayout ,
    path: '/printer',
    name: '打印管理',
    redirect: '/printer/index',
    children: [
      {
        path: 'index',
        name: 'PrinterList',
        component: () => import('#/views/printer/index.vue'),
        meta: {
          title: '打印机列表',
          icon: 'mdi:printer',
          hideInMenu: true,
        }

      }
    ]
  }
] as RouteRecordRaw[];
