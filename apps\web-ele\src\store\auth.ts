import type { Recordable, UserInfo } from '@vben/types';

import { ref } from 'vue';
import { useRouter } from 'vue-router';

import { DEFAULT_HOME_PATH, LOGIN_PATH } from '@vben/constants';
import { resetAllStores, useAccessStore, useUserStore } from '@vben/stores';

import { ElNotification } from 'element-plus';
import { defineStore } from 'pinia';

import { getAccessCodesApi, getUserInfoApi, loginApi, logoutApi } from '#/api';
import { $t } from '#/locales';
import md5 from 'js-md5';

export const useAuthStore = defineStore('auth', () => {
  const accessStore = useAccessStore();
  const userStore = useUserStore();
  const router = useRouter();

  const loginLoading = ref(false);

  /**
   * 异步处理登录操作
   */
  async function authLogin(
    params: Recordable<any>,
    onSuccess?: () => Promise<void> | void,
  ) {
    let userInfo: null | UserInfo = null;
    try {
      loginLoading.value = true;
      console.log('params', md5(params.password));
      const encryptedPassword = md5(params.password);

      let data = await loginApi({...params, password: encryptedPassword});

      if (data?.token) {
        accessStore.setAccessToken(data?.token);
        userInfo = data;
        userStore.setUserInfo(userInfo);

        // 登录成功后初始化MQTT连接
        await initializeMqttConnection(userInfo, params.password);

        if (accessStore.loginExpired) {
          accessStore.setLoginExpired(false);
        } else {
          onSuccess
            ? await onSuccess?.()
            : await router.push('/printer');
        }

        if (userInfo?.name) {
          ElNotification({
            message: `${$t('authentication.loginSuccessDesc')}:${userInfo?.name}`,
            title: $t('authentication.loginSuccess'),
            type: 'success',
          });
        }
      }
    } finally {
      loginLoading.value = false;
    }

    return {
      userInfo,
    };
  }

  /**
   * 初始化MQTT连接
   */
  async function initializeMqttConnection(userInfo: UserInfo, password: string) {
    try {
      console.log('开始初始化MQTT连接...');

      // 动态导入mqttService避免循环依赖
      const { mqttService } = await import('#/services/mqttService');

      // 对密码进行MD5加密，与登录API保持一致
      const encryptedPassword = md5(password);

      // 设置用户认证信息用于MQTT连接
      await mqttService.setUserCredentials({
        username: userInfo.mobile || userInfo.name, // 使用手机号或用户名
        password: encryptedPassword, // 使用MD5加密后的密码
        employeeId: userInfo.employeeId,
        shopId: userInfo.shopId
      });

      // 尝试连接MQTT
      const connected = await mqttService.connect();

      if (connected) {
        console.log('MQTT连接初始化成功');
      } else {
        console.warn('MQTT连接初始化失败，但不影响登录');
      }

    } catch (error) {
      console.error('MQTT连接初始化出错:', error);
      // 不抛出错误，避免影响登录流程
    }
  }

  async function logout(redirect: boolean = true) {
    try {
      // 先断开MQTT连接
      await disconnectMqtt();

      await logoutApi();
    } catch {
      // 不做任何处理
    }

    // 不要调用 resetAllStores()，而是手动重置各个 store
    accessStore.setAccessToken(null);
    accessStore.setLoginExpired(false);
    userStore.setUserInfo(null);

    // 回登录页带上当前路由地址
    await router.replace({
      path: LOGIN_PATH,
      query: redirect
        ? {
            redirect: encodeURIComponent(router.currentRoute.value.fullPath),
          }
        : {},
    });
  }

  /**
   * 断开MQTT连接
   */
  async function disconnectMqtt() {
    try {
      console.log('正在断开MQTT连接...');
      const { mqttService } = await import('#/services/mqttService');
      await mqttService.disconnect();
      console.log('MQTT连接已断开');
    } catch (error) {
      console.error('断开MQTT连接时出错:', error);
    }
  }

  async function fetchUserInfo() {
    let userInfo: null | UserInfo = null;
    userInfo = await getUserInfoApi();
    userStore.setUserInfo(userInfo);
    return userInfo;
  }

  function $reset() {
    loginLoading.value = false;
  }

  return {
    $reset,
    authLogin,
    fetchUserInfo,
    loginLoading,
    logout,
  };
}, {
  // 添加 persist 配置以避免 setup store 的问题
  persist: false
});



