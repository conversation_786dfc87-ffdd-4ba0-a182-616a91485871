<template>
  <div class="file-details-component">
    <div class="details-header">
      <div class="summary">
        <h3 class="title">订单文件详情</h3>
        <span class="stat">合计文件: {{ printStore.summaryForDisplay.total }}</span>
      </div>
      <div class="actions">
        <template v-if="printStore.isClaimOrder">
          <el-button type="primary" @click="printStore.printAllWithoutOrderPage()">打印全部(不含订单页)</el-button>
          <el-button type="primary" @click="printStore.printAllWithOrderPage()">打印全部</el-button>
          <el-button type="success" @click="printStore.printSelectedFiles()">打印选中文件</el-button>
        </template>
        <template v-else>
          <el-button type="primary" @click="printStore.claimOrder()">认领任务</el-button>
          <el-button type="danger" @click="handleRejectOrder">拒单</el-button>

          <!-- <el-button type="danger" @click="downFiles">提取文件</el-button> -->

        </template>
      </div>
    </div>

    <div class="table-wrapper">
      <el-table
        :data="printStore.filesForDisplay"
        style="width: 100%"
        height="100%"
        highlight-current-row
        @row-click="handleRowClick"
        @selection-change="printStore.updateFileSelection"
        :row-class-name="getRowClassName"
      >
        <el-table-column type="selection" width="55" :selectable="(row) => row.status !== '3'" />
        <el-table-column type="index" label="序号" width="60" />
        <el-table-column prop="fileType" label="类型" width="80">
          <template #default="scope">
            <el-tag v-if="scope.row.fileType === '1'" type="info"> 文档 </el-tag>
            <el-tag v-if="scope.row.fileType === '2'" type="warning"> 封面 </el-tag>
            <el-tag v-if="scope.row.fileType === '3'" type="info"> 图片 </el-tag>
            <el-tag v-if="scope.row.fileType === '4'" type="info"> 订单 </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="fileName" label="文件名称" min-width="180" />
        <el-table-column prop="copies" label="份数" width="70" />
        <el-table-column prop="size" label="大小" width="70" />
        <el-table-column prop="pageTotal" label="总页数" width="80" />
        <el-table-column prop="startPageNumber" label="起始页" width="80" />
        <el-table-column prop="endPageNumber" label="结束页" width="80" />
        <!-- <el-table-column prop="range" label="打印范围" width="90" /> -->
        <el-table-column prop="duplex" label="单双面" width="90" />
        <el-table-column prop="color" label="色彩" width="100" />
        <el-table-column prop="paper" label="纸张" width="90" />
        <el-table-column prop="status" label="状态" width="90">
          <template #default="scope">
            <span :class="`status-${scope.row.status}`">{{ getStatusText(scope.row.status) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button
              link
              type="primary"
              size="small"
              @click.stop="handlePrintSingle(scope.row)"
            >
              显性打印
            </el-button>
            <el-button
              link
              type="success"
              size="small"
              @click.stop="handleDownloadFile(scope.row)"
            >
              下载
            </el-button>
            <el-button
              link
              type="danger"
              size="small"
              :disabled="scope.row.status === '3'"
              @click.stop="handleReportIllegalFile(scope.row)"
            >
              举报
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits , inject} from 'vue';
import type { PropType } from 'vue';
import { ElButton, ElTable, ElTableColumn, ElTag, ElMessage, ElMessageBox } from 'element-plus';
import { usePrintStore } from '#/stores/printStore';
import { rejectOrder, printFileReport } from '#/api/wosys/printer';
import { aardio } from '#/aardio';
import { downloadFileFromUrl } from '@vben/utils';

const printStore = usePrintStore();

import type {
  PrintFile
} from '#/types/print.types';

// --- 方法 ---
const getStatusText = (status: any): string => ({
  '0': '待打印',
  '1': '打印成功',
  '2': '打印失败',
  '3': '已举报'
}[status] || '未知');

const getRowClassName = ({ row }: { row: any }) => {
  let className = 'clickable-row';
  if (row.status === '3') {
    className += ' reported-file-row';
  }
  return className;
};

const handleRowClick = (file: PrintFile) => {
  printStore.selectFileForPreview(file);
}

const downFiles = async()=>{
  await aardio.downFiles();
}

// 处理单个文件打印
const handlePrintSingle = async (file: PrintFile) => {
  if (!printStore.isClaimOrder) {
    ElMessage.warning('请先认领任务后再进行打印操作');
    return;
  }

  try {
    await printStore.printSingleFile(file, true);
  } catch (error) {
    console.error('打印文件失败:', error);
    ElMessage.error('打印文件失败，请重试');
  }
}

// 处理文件下载
const handleDownloadFile = async (file: PrintFile) => {
  try {
    // 使用 ElMessageBox 让用户选择下载目录
    await ElMessageBox.prompt(
      '请输入下载目录路径（留空使用默认下载目录）',
      '选择下载目录',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPlaceholder: '例如: C:\\Downloads',
        inputValue: '', // 默认为空，使用系统默认下载目录
      }
    );

    // 调用下载功能
    await downloadFileFromUrl({
      source: file.fileUrl,
      fileName: file.fileName,
      target: '_self'
    });

    ElMessage.success('文件下载已开始');
  } catch (error) {
    if (error !== 'cancel') {
      console.error('下载文件失败:', error);
      ElMessage.error('下载文件失败，请重试');
    }
  }
}

// 处理非法文件举报
const handleReportIllegalFile = async (file: PrintFile) => {
  try {
    // 使用 ElMessageBox 让用户输入举报原因
    const { value: reportReason } = await ElMessageBox.prompt(
      '请输入举报原因',
      '举报非法文件',
      {
        confirmButtonText: '提交举报',
        cancelButtonText: '取消',
        inputPlaceholder: '请详细描述文件存在的问题...',
        inputType: 'textarea',
        inputValidator: (value) => {
          if (!value || value.trim().length === 0) {
            return '请输入举报原因';
          }
          if (value.trim().length < 5) {
            return '举报原因至少需要5个字符';
          }
          return true;
        }
      }
    );

    // 调用举报API
    await printFileReport({
      printFileId: file.fileId,
      printResult: 3, // 3表示非法文件
      printResultDesc: reportReason.trim(),
      printResultTime: new Date().toISOString()
    });

    // 更新本地文件状态 - 使用fileKey而不是fileId
    printStore.updateFileStatus(file.fileKey, '3');

    ElMessage.success('举报已提交，感谢您的反馈');
  } catch (error) {
    if (error !== 'cancel') {
      console.error('提交举报失败:', error);
      ElMessage.error('提交举报失败，请重试');
    }
  }
}

// 处理拒单
const handleRejectOrder = async () => {
  if (!printStore.selectedOrder) {
    ElMessage.warning('请先选择订单');
    return;
  }

  try {
    const { value: reason } = await ElMessageBox.prompt(
      '请输入拒单原因',
      '拒绝订单',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /\S+/,
        inputErrorMessage: '拒单原因不能为空'
      }
    );

    if (reason && reason.trim()) {
      await rejectOrder({
        bussId: printStore.selectedOrder.bussId,
        reason: reason.trim()
      });

      ElMessage.success('订单已拒绝');

      // 刷新订单列表
      await printStore.fetchOrders(1, { pageNum: 1, pageSize: 10 }, true);

      // 清空当前选中的订单
      printStore.selectOrder('');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('拒单失败:', error);
      ElMessage.error('拒单失败，请重试');
    }
  }
}

// 执行批量打印
const batchPrintClick = () => {
  printActions.batchPrint();
};
</script>

<style scoped lang="scss">

.file-details-component {
  background-color: #2d3748; color: #e2e8f0; padding: 16px; border-radius: 8px; display: flex; flex-direction: column; gap: 16px;
}
.details-header {
  display: flex; justify-content: space-between; align-items: center; flex-shrink: 0;
  .summary { display: flex; align-items: center; gap: 20px; }
  .title { font-size: 18px; font-weight: 600; margin: 0; color: #fff; }
  .stat { font-size: 14px; color: #a0aec0; }
  :deep(.el-button--warning) { --el-button-bg-color: #f0a020; --el-button-border-color: #f0a020; --el-button-hover-bg-color: #f3b450; --el-button-hover-border-color: #f3b450; }
}
.print-settings-bar {
  background-color: #374151; padding: 8px 12px; border-radius: 4px; font-size: 13px; color: #c1c8d4; flex-shrink: 0;
}
.table-wrapper {
  flex: 1; min-height: 0;
  :deep(.clickable-row) { cursor: pointer; }
  :deep(.el-table) {
    --el-table-bg-color: #2d3748; --el-table-tr-bg-color: #2d3748; --el-table-header-bg-color: #1F2937; --el-table-row-hover-bg-color: #374151; --el-table-border-color: #4a5568; --el-table-text-color: #e2e8f0; --el-table-header-text-color: #a0aec0;

    /* [新增] 覆盖选中行的背景色 */
    .el-table__row.current-row > td.el-table__cell {
      background-color: #4a5568 !important;
    }

    th.el-table__cell, td.el-table__cell { border-right: none; }
    .el-table__row { border-bottom: 1px solid var(--el-table-border-color); }
    .el-table__row:last-child { border-bottom: none; }
    .el-table__header-wrapper th { font-weight: 600; }
    .status-1 { color: #34d399; } .status-0 { color: #f0a020; } .status-2 { color: #ef4444; } .status-3 { color: #f59e0b; font-weight: bold; }

    /* 已举报文件的行样式 */
    .reported-file-row {
      background-color: rgba(245, 158, 11, 0.1) !important;
    }

    /* 禁用的按钮样式 */
    .el-button:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
  :deep(.el-table__body-wrapper::-webkit-scrollbar) { width: 8px; height: 8px; }
  :deep(.el-table__body-wrapper::-webkit-scrollbar-track) { background: #1a202c; border-radius: 4px; }
  :deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) { background: #4a5568; border-radius: 4px; &:hover { background: #a0aec0; } }
}
</style>




