<template>
  <div class="print-order-page p-4">
    <div class="tabs-container">
      <el-tabs v-model="activeTabName" class="order-tabs" @tab-change="tabChanged">
        <el-tab-pane label="打印订单" name="1"></el-tab-pane>
        <el-tab-pane label="历史订单" name="2"></el-tab-pane>
        <el-tab-pane label="异常订单" name="3"></el-tab-pane>
      </el-tabs>

      <!-- 搜索按钮 - 只在历史订单tab显示 -->
      <div v-if="activeTabName === '2'" class="search-button-container">
        <el-button
          type="primary"
          :icon="Search"
          @click="showSearchDrawer = true"
          size="default"
          class="search-trigger-btn"
        >
          搜索
        </el-button>
      </div>
    </div>

    <div v-loading="loading" class="order-list" element-loading-background="rgba(0, 0, 0, 0.5)">
      <el-scrollbar
        height="83vh"
        @scroll="handleScroll"
        ref="scrollbarRef"
      >
        <el-empty v-if="!loading && printStore.orderList.length === 0" description="暂无相关订单" />
        <el-card
          v-else
          v-for="order in printStore.orderList"
          :key="order.bussId"
          class="order-card"
          shadow="never"
          :class="{ 'is-active': order.bussId === printStore.selectedOrderId }"
          @click="handleCardClick(order.bussId)"
          style="cursor: pointer;"
        >
          <div class="card-content">
            <div class="icon-wrapper" :class="`icon-${order.orderType==1 ? 'file' : 'photo'}`">
              <el-icon :size="32" color="#fff">
                <component :is="getIcon(order.orderType)" />
              </el-icon>
            </div>

            <div class="info-wrapper">
              <div class="info-line"><span class="label">订单编号：</span><span class="value">{{ order.bussId }}</span></div>
              <div class="info-line"><span class="label">打印类型 ： {{ order.orderType === '1' ? '文件' : '照片' }} - ¥ {{ order.payAmount.toFixed(2) }}</span></div>
              <div class="info-line"><span class="label">创建时间：</span><span class="value">{{ order.updateTime }}</span></div>
              <div class="info-line"><span class="label">状态:</span><span class="value">{{ getStatusText(order.status) }}</span></div>
              <div class="info-line"><span class="label">打印方式:</span><span class="value">手动打印</span></div>
            </div>
          </div>
        </el-card>

        <!-- 分页加载指示器 -->
        <div v-if="hasMore && !loadingMore && printStore.orderList.length > 0" class="load-more-indicator">
          <div class="load-more-text">下拉加载更多</div>
        </div>

        <div v-if="loadingMore" class="loading-more-indicator">
          <el-icon class="is-loading"><Loading /></el-icon>
          <span>加载中...</span>
        </div>

        <div v-if="!hasMore && printStore.orderList.length > 0" class="no-more-indicator">
          <span>没有更多数据了</span>
        </div>
      </el-scrollbar>
    </div>

    <!-- 搜索弹出层 -->
    <SearchDrawer
      v-model="showSearchDrawer"
      @search="handleDrawerSearch"
      @reset="handleDrawerReset"
      ref="searchDrawerRef"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import type { PropType, Component } from 'vue';
import { ElTabs, ElTabPane, ElIcon, ElCard, ElEmpty, ElButton, ElScrollbar } from 'element-plus';
import { Search, Document, User, Loading } from '@element-plus/icons-vue';
import { usePrintStore } from '#/stores/printStore';
import type { ApiOrder } from '#/types/print.types';
import SearchDrawer from './SearchDrawer.vue';

const printStore = usePrintStore();
const activeTabName = ref('1');
const showSearchDrawer = ref(false);
const searchDrawerRef = ref();

// 搜索条件
const searchParams = ref({
  dateRange: null as [Date, Date] | null,
  statusFilter: '',
  searchText: ''
});

// 分页相关状态
const currentPage = ref(1);
const pageSize = ref(10);
const hasMore = ref(true);
const loadingMore = ref(false);
const scrollbarRef = ref();

const loading = computed(() => printStore.isLoading);

const handleDrawerSearch = (params: any) => {
  searchParams.value = { ...params };
  handleSearch();
};

const handleDrawerReset = () => {
  searchParams.value = {
    dateRange: null,
    statusFilter: '',
    searchText: ''
  };
  handleSearch();
};

const handleSearch = () => {
  currentPage.value = 1;
  hasMore.value = true;

  const params: any = {
    queryType: activeTabName.value,
    pageNum: currentPage.value,
    pageSize: pageSize.value
  };

  // 历史订单搜索参数
  if (activeTabName.value === '2') {
    if (searchParams.value.dateRange) {
      params.startDate = searchParams.value.dateRange[0];
      params.endDate = searchParams.value.dateRange[1];
    }
    if (searchParams.value.statusFilter) {
      params.status = searchParams.value.statusFilter;
    }
    if (searchParams.value.searchText) {
      params.bussId = searchParams.value.searchText;
    }
  }

  printStore.fetchOrders(activeTabName.value, params, true);
};

const tabChanged = (e) => {
  console.log('Tab changed to:', e);
  // 重置搜索条件
  searchParams.value = {
    dateRange: null,
    statusFilter: '',
    searchText: ''
  };

  // 同步更新搜索弹出层的表单
  if (searchDrawerRef.value) {
    searchDrawerRef.value.setSearchForm(searchParams.value);
  }

  currentPage.value = 1;
  hasMore.value = true;

  printStore.fetchOrders(e, {
    pageNum: currentPage.value,
    pageSize: pageSize.value
  }, true).then(() => {
    console.log('切换tab后订单数据:', printStore.orderList);
  });
};

const loadMore = async () => {
  if (loadingMore.value || !hasMore.value || loading.value) return;

  loadingMore.value = true;
  currentPage.value += 1;

  const params: any = {
    queryType: activeTabName.value,
    pageNum: currentPage.value,
    pageSize: pageSize.value
  };

  if (activeTabName.value === '2') {
    if (searchParams.value.dateRange) {
      params.startDate = searchParams.value.dateRange[0];
      params.endDate = searchParams.value.dateRange[1];
    }
    if (searchParams.value.statusFilter) {
      params.status = searchParams.value.statusFilter;
    }
    if (searchParams.value.searchText) {
      params.bussId = searchParams.value.searchText;
    }
  }

  try {
    const result = await printStore.fetchOrders(activeTabName.value, params, false);
    if (!result || result.length < pageSize.value) {
      hasMore.value = false;
    }
  } catch (error) {
    console.error('加载更多数据失败:', error);
    currentPage.value -= 1;
  } finally {
    loadingMore.value = false;
  }
};

// 防抖处理滚动事件
let scrollTimer: NodeJS.Timeout | null = null;
const handleScroll = ({ scrollTop, scrollLeft }) => {
  if (scrollTimer) {
    clearTimeout(scrollTimer);
  }

  scrollTimer = setTimeout(() => {
    const scrollbar = scrollbarRef.value;
    if (!scrollbar || !hasMore.value || loadingMore.value || loading.value) return;

    const { clientHeight, scrollHeight } = scrollbar.wrapRef;

    // 当滚动到底部附近时自动加载更多，增加阈值避免频繁触发
    if (scrollHeight - scrollTop - clientHeight < 50 && printStore.orderList.length >= pageSize.value) {
      loadMore();
    }
  }, 100);
};

const getIcon = (type: '1' | '2'): Component => (type === '1' ? Document : User);
const getStatusText = (status: ApiOrder['status']): string => ({
  '1': '待打印',
  '2': '已确认(自动)',
  '3': '已确认(手动)',
  '4': '打印中',
  '5': '打印完成',
  '6': '打印失败',
}[status] || '未知状态');

const handleCardClick = (orderId: string) => {
  printStore.selectOrder(orderId);
};

onMounted(() => {
  console.log('OrderCardList mounted, 开始获取订单数据');
  printStore.fetchOrders(activeTabName.value, {
    queryType: 1,
    pageNum: currentPage.value,
    pageSize: pageSize.value
  }, true).then(() => {
    console.log('订单数据获取完成:', printStore.orderList);
  });
});
</script>
<style scoped lang="scss">
.print-order-page {
  background-color: #1F2937;
  padding: 8px;
  border-radius: 8px;
  min-height: 93vh;
  display: flex;
  flex-direction: column;
}

.tabs-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
}

:deep(.order-tabs) {
  flex: 1;
  min-width: 300px;

  .el-tabs__header {
    margin: 0;
  }

  .el-tabs__nav-wrap::after {
    background-color: #4a5568;
  }

  .el-tabs__active-bar {
    display: none !important;
  }

  .el-tabs__item {
    color: #a0aec0;
    padding: 0 10px !important;
    position: relative;

    &.is-active {
      color: #409EFF;
    }

    &.is-active::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 10px;
      right: 10px;
      height: 2px;
      background-color: #409EFF;
    }
  }
}

.search-button-container {
  flex-shrink: 0;
  margin-left: 16px;
}

:deep(.search-trigger-btn) {
  background-color: #409EFF;
  border-color: #409EFF;

  &:hover {
    background-color: #66b1ff;
    border-color: #66b1ff;
  }
}

.order-list {
  display: grid;
  gap: 16px;
  margin-top: 16px;
  flex-grow: 1;
  overflow-y: auto;
  min-height: 0;
}

:deep(.el-empty__description p) {
  color: #a0aec0;
}

.order-card {
  background-color: #2d3748;
  border: none;
  border-radius: 8px;
  margin-bottom: 8px;
}

.order-card.is-active {
  border: 1px solid var(--el-color-primary);
  box-shadow: 0 0 10px rgba(64, 158, 255, 0.3);
}

.card-content {
  width: 400px;
  display: flex;
  gap: 16px;
  align-items: center;
}

.icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border-radius: 8px;
  flex-shrink: 0;

  &.icon-file {
    background-color: #4299e1;
  }

  &.icon-photo {
    background-color: #ed8936;
  }

  :deep(.el-icon), :deep(svg) {
    color: #fff !important;
  }
}

.info-wrapper {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 14px;
  min-width: 0;

  .info-line {
    display: flex;
    align-items: center;
    white-space: nowrap;
  }

  .label {
    color: #a0aec0;
    flex-shrink: 0;
    display: inline-block;
    width: 75px;
  }

  .value {
    color: #e2e8f0;
  }

  .price {
    color: #e2e8f0;
    font-weight: 500;
  }
}

/* 分页指示器样式 */
.load-more-indicator {
  text-align: center;
  padding: 12px;
  color: #6b7280;
  font-size: 12px;
}

.loading-more-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px;
  color: #6b7280;
  gap: 6px;
  font-size: 12px;
}

.no-more-indicator {
  text-align: center;
  padding: 12px;
  color: #6b7280;
  font-size: 12px;
}

/* 美化滚动条 */
.order-list::-webkit-scrollbar {
  width: 6px;
}

.order-list::-webkit-scrollbar-track {
  background: #1a202c;
}

.order-list::-webkit-scrollbar-thumb {
  background: #4a5568;
  border-radius: 3px;

  &:hover {
    background: #a0aec0;
  }
}
</style>



