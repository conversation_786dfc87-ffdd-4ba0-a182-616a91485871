// MQTT配置
export const mqttConfig = {
  // 从环境变量获取或使用默认值
  host: import.meta.env.VITE_MQTT_HOST || 'dev.wosys.cn',
  port: import.meta.env.VITE_MQTT_WS_PORT || 10004, // WebSocket端口
  username: import.meta.env.VITE_MQTT_USERNAME || 'print_user',
  password: import.meta.env.VITE_MQTT_PASSWORD || 'print_password',
  topicPrefix: import.meta.env.VITE_MQTT_TOPIC_PREFIX || 'shop',
  qos: 1 as 0 | 1 | 2,

  // 生成状态上报主题
  generateStatusTopic: (shopId: string, employeeId: string) =>
    `print/shopId/${shopId}/employeeId/${employeeId}/status`
};



