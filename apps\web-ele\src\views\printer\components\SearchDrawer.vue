<template>
  <el-drawer
    v-model="visible"
    title="搜索条件"
    direction="rtl"
    size="400px"
    :before-close="handleClose"
  >
    <div class="search-form">
      <el-form :model="searchForm" label-width="80px" label-position="top">
        <el-form-item label="日期范围">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            size="large"
            style="width: 100%"
            class="search-date-picker"
          />
        </el-form-item>

        <el-form-item label="订单状态">
          <el-select
            v-model="searchForm.statusFilter"
            placeholder="选择状态"
            size="large"
            clearable
            style="width: 100%"
            class="search-select"
          >
            <el-option label="全部" value="" />
            <el-option label="打印完成" value="5" />
            <el-option label="打印失败" value="6" />
          </el-select>
        </el-form-item>

        <el-form-item label="订单编号">
          <el-input
            v-model="searchForm.searchText"
            placeholder="请输入订单编号"
            size="large"
            clearable
            class="search-input"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleReset" size="large">重置</el-button>
        <el-button type="primary" @click="handleSearch" size="large">搜索</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { ElDrawer, ElForm, ElFormItem, ElDatePicker, ElSelect, ElOption, ElInput, ElButton, ElIcon } from 'element-plus';
import { Search } from '@element-plus/icons-vue';

interface SearchForm {
  dateRange: [Date, Date] | null;
  statusFilter: string;
  searchText: string;
}

const props = defineProps<{
  modelValue: boolean;
}>();

const emit = defineEmits<{
  'update:modelValue': [value: boolean];
  'search': [params: SearchForm];
  'reset': [];
}>();

const visible = ref(false);
const searchForm = ref<SearchForm>({
  dateRange: null,
  statusFilter: '',
  searchText: ''
});

watch(() => props.modelValue, (val) => {
  visible.value = val;
});

watch(visible, (val) => {
  emit('update:modelValue', val);
});

const handleClose = () => {
  visible.value = false;
};

const handleSearch = () => {
  emit('search', { ...searchForm.value });
  visible.value = false;
};

const handleReset = () => {
  searchForm.value = {
    dateRange: null,
    statusFilter: '',
    searchText: ''
  };
  emit('reset');
};

// 暴露方法供父组件调用
const setSearchForm = (form: Partial<SearchForm>) => {
  Object.assign(searchForm.value, form);
};

defineExpose({
  setSearchForm
});
</script>

<style scoped lang="scss">
.search-form {
  padding: 20px 0;
}

:deep(.el-drawer__header) {
  background-color: #2d3748;
  color: #e2e8f0;
  border-bottom: 1px solid #4a5568;
  margin-bottom: 0;
  padding: 20px;
}

:deep(.el-drawer__body) {
  background-color: #1f2937;
  color: #e2e8f0;
  padding: 0 20px;
}

:deep(.el-drawer__footer) {
  background-color: #2d3748;
  border-top: 1px solid #4a5568;
  padding: 20px;
}

:deep(.el-form-item__label) {
  color: #e2e8f0;
  font-weight: 500;
}

:deep(.search-date-picker) {
  .el-input__wrapper {
    background-color: #374151;
    box-shadow: none;
    border: 1px solid #4a5568;
  }
  .el-input__inner {
    color: #e2e8f0;
  }
  .el-input__prefix,
  .el-input__suffix {
    color: #a0aec0;
  }
}

:deep(.search-select) {
  .el-select__wrapper {
    background-color: #374151;
    box-shadow: none;
    border: 1px solid #4a5568;
  }
  .el-select__placeholder {
    color: #a0aec0;
  }
  .el-select__selected-item {
    color: #e2e8f0;
  }
  .el-select__caret {
    color: #a0aec0;
  }
}

:deep(.search-input) {
  .el-input__wrapper {
    background-color: #374151;
    box-shadow: none;
    border: 1px solid #4a5568;
  }
  .el-input__inner {
    color: #e2e8f0;
  }
  .el-input__prefix {
    color: #a0aec0;
  }
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-button) {
  &.el-button--default {
    background-color: #374151;
    border-color: #4a5568;
    color: #e2e8f0;

    &:hover {
      background-color: #4a5568;
      border-color: #6b7280;
    }
  }

  &.el-button--primary {
    background-color: #409EFF;
    border-color: #409EFF;

    &:hover {
      background-color: #66b1ff;
      border-color: #66b1ff;
    }
  }
}
</style>
