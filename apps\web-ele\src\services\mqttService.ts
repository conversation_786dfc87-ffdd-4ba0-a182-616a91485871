import mqtt from 'mqtt';
import { ElMessage } from 'element-plus';
import { mqttConfig } from '#/config/mqtt';
import { useUserStore } from '@vben/stores';
import { usePrintStore } from '#/stores/printStore';
import { pa } from 'element-plus/es/locales.mjs';

import {
  orderConfirm
} from '#/api/wosys/printer';

export interface MqttMessage {
  bussId: string;
  shopId: string;
  orderData: any;
  timestamp: string;
  type: 'new_order' | 'order_update' | 'print_command';
}

export class MqttService {
  private client: mqtt.MqttClient | null = null;
  private isConnected = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectInterval = 5000; // 5秒
  private reconnectTimer: NodeJS.Timeout | null = null;
  private userStore = useUserStore();
  private printStore = usePrintStore();
  private connectionConfig: any = null;
  private subscriptionTopic = '';
  private userCredentials: any = null; // 存储用户凭据

  constructor() {
    // 监听用户登录状态变化
    this.setupUserStateWatcher();
  }

  /**
   * 设置用户凭据
   */
  async setUserCredentials(credentials: {
    username: string;
    password: string;
    employeeId: string;
    shopId: string;
  }) {
    this.userCredentials = credentials;
    console.log('MQTT用户凭据已设置:', {
      username: credentials.username,
      employeeId: credentials.employeeId,
      shopId: credentials.shopId
    });
  }

  /**
   * 初始化MQTT连接
   */
  async connect(): Promise<boolean> {
    try {
      const userInfo = this.userStore.userInfo;
      if (!userInfo?.shopId) {
        console.warn('用户信息不完整，无法初始化MQTT连接');
        return false;
      }

      if (!this.userCredentials) {
        console.warn('用户凭据未设置，无法初始化MQTT连接');
        return false;
      }

      // 如果已经连接，先断开
      if (this.client && this.isConnected) {
        await this.disconnect();
      }

      // 构建连接配置，使用用户凭据
      this.connectionConfig = {
        clientId: this.userCredentials.employeeId, // 使用员工编号作为clientId
        username: this.userCredentials.employeeId, // 使用登录用户名
        password: this.userCredentials.password, // 使用MD5加密后的密码
        clean: true,
        reconnectPeriod: 0, // 禁用自动重连，我们自己处理
        connectTimeout: 10000,
        keepalive: 60,
        protocolVersion: 4,
      };

      // 订阅主题格式: print/shopId/{shopId}/employeeId/{employeeId}/view
      this.subscriptionTopic = `print/shopId/${userInfo.shopId}/employeeId/${this.userCredentials.employeeId}/view`;

      console.log('正在连接MQTT服务器...', {
        host: mqttConfig.host,
        port: mqttConfig.port,
        clientId: this.connectionConfig.clientId,
        username: this.connectionConfig.username,
        password: this.connectionConfig.password,
        topic: this.subscriptionTopic,
        employeeId: this.userCredentials.employeeId
      });

      // 构建连接URL
      const brokerUrl = `wss://${mqttConfig.host}/mqtt_ws`;

      // 创建MQTT客户端
      this.client = mqtt.connect(brokerUrl, this.connectionConfig);

      // 设置事件监听器
      this.setupEventListeners();

      // 等待连接完成
      return new Promise((resolve) => {
        const timeout = setTimeout(() => {
          console.error('MQTT连接超时');
          resolve(false);
        }, 15000);

        this.client!.on('connect', () => {
          clearTimeout(timeout);
          resolve(true);
        });

        this.client!.on('error', () => {
          clearTimeout(timeout);
          resolve(false);
        });
      });

    } catch (error) {
      console.error('MQTT连接失败:', error);
      this.isConnected = false;
      this.scheduleReconnect();
      ElMessage.error(`MQTT连接失败: ${error.message || '未知错误'}`);
      return false;
    }
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners() {
    if (!this.client) return;

    // 连接成功
    this.client.on('connect', () => {
      console.log('MQTT连接成功');
      this.isConnected = true;
      this.reconnectAttempts = 0;

      // 订阅主题
      this.client!.subscribe(this.subscriptionTopic, { qos: mqttConfig.qos }, (err) => {
        if (err) {
          console.error('订阅主题失败:', err);
          ElMessage.error('MQTT主题订阅失败');
        } else {
          console.log('成功订阅主题:', this.subscriptionTopic);
          ElMessage.success('MQTT服务连接成功');
        }
      });

      // 清除重连定时器
      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer);
        this.reconnectTimer = null;
      }
    });

    // 接收消息
    this.client.on('message', async (topic, message) => {
      try {
        const messageStr = message.toString();
        console.log('收到MQTT消息:', topic, messageStr);

        const parsedMessage: MqttMessage = JSON.parse(messageStr);
        await this.handleMessage(parsedMessage);

      } catch (error) {
        console.error('处理MQTT消息失败:', error);
        ElMessage.error('处理服务器消息失败');
      }
    });

    // 连接断开
    this.client.on('close', () => {
      console.log('MQTT连接已断开');
      this.isConnected = false;

      // 如果不是主动断开，启动重连
      if (this.connectionConfig) {
        this.scheduleReconnect();
      }
    });

    // 连接错误
    this.client.on('error', (error) => {
      console.error('MQTT连接错误:', error);
      this.isConnected = false;
      ElMessage.error(`MQTT连接错误: ${error.message}`);

      // 启动重连机制
      this.scheduleReconnect();
    });

    // 重连事件
    this.client.on('reconnect', () => {
      console.log('MQTT正在重连...');
    });

    // 离线事件
    this.client.on('offline', () => {
      console.log('MQTT客户端离线');
      this.isConnected = false;
    });
  }

  /**
   * 断开MQTT连接
   */
  async disconnect(): Promise<void> {
    try {
      // 清除重连定时器
      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer);
        this.reconnectTimer = null;
      }

      if (this.client && this.isConnected) {
        // 取消订阅
        if (this.subscriptionTopic) {
          this.client.unsubscribe(this.subscriptionTopic);
        }

        // 断开连接
        this.client.end(true);
        console.log('MQTT连接已断开');
      }

      this.isConnected = false;
      this.reconnectAttempts = 0;
      this.connectionConfig = null;
      this.userCredentials = null; // 清除用户凭据
      this.client = null;

    } catch (error) {
      console.error('断开MQTT连接时出错:', error);
    }
  }

  /**
   * 处理接收到的MQTT消息
   */
  private async handleMessage(message: MqttMessage) {
    const { type, bussId, orderData } = message;

     await this.handleNewOrder(bussId, orderData);
  }

  /**
   * 处理新订单消息
   */
  private async handleNewOrder(bussId: string, orderData: any) {
    try {
      console.log('处理新订单:', bussId, orderData);

      // 播报收到新订单
      this.playVoiceNotification('收到新的订单请查收');
      ElMessage.info(`收到新订单: ${bussId}`);

      // 刷新订单列表
      await this.printStore.fetchOrders(1);
      console.log("mode--------------------------",this.printStore.globalPrintSettings.mode);
      // 检查是否开启了自动打印
      if (this.printStore.globalPrintSettings.mode === 'auto') {
        await this.processAutoOrder(bussId);
      }

    } catch (error) {
      console.error('处理新订单失败:', error);
      ElMessage.error(`处理新订单 ${bussId} 失败`);
    }
  }

  /**
   * 处理订单更新消息
   */
  private async handleOrderUpdate(bussId: string, orderData: any) {
    try {
      console.log('处理订单更新:', bussId, orderData);

      // 刷新订单列表
      await this.printStore.fetchOrders(1);

      ElMessage.info(`订单 ${bussId} 状态已更新`);

    } catch (error) {
      console.error('处理订单更新失败:', error);
    }
  }

  /**
   * 处理打印命令消息
   */
  private async handlePrintCommand(bussId: string, commandData: any) {
    try {
      console.log('处理打印命令:', bussId, commandData);

      // 选择对应订单
      this.printStore.selectOrder(bussId);

      // 等待订单选择完成
      await new Promise(resolve => setTimeout(resolve, 500));

      // 执行打印命令
      if (commandData.action === 'print_all') {
        await this.printStore.printAllWithOrderPage();
      } else if (commandData.action === 'print_files') {
        await this.printStore.printAllWithoutOrderPage();
      }

    } catch (error) {
      console.error('处理打印命令失败:', error);
    }
  }

  /**
   * 自动处理订单
   */
  private async processAutoOrder(bussId: string) {
    try {
      console.log('自动打印模式已开启，处理订单:', bussId);

      // 选择新订单
      this.printStore.selectOrder(bussId);

      // 等待订单选择完成
      await new Promise(resolve => setTimeout(resolve, 500));

      // 检查是否满足自动打印条件
      const currentOrder = this.printStore.selectedOrder;
      if (currentOrder && currentOrder.bussId === bussId) {
        const printPlan = this.printStore.findAvailablePrintersForOrder(
          this.printStore.filesForDisplay
        );

        if (printPlan !== false) {
          console.log('满足自动打印条件，开始自动认领和打印');

          // 自动认领任务
          await orderConfirm({
            bussId: bussId , employeeId: this.userCredentials.employeeId , confirmType: 1
          });

          // 等待认领完成
          await new Promise(resolve => setTimeout(resolve, 1000));

          // 自动执行打印
          await this.printStore.executeSmartPrint(
            this.printStore.filesForDisplay,
            true
          );

          ElMessage.success(`订单 ${bussId} 已自动认领并开始打印`);
        } else {
          console.log('不满足自动打印条件，跳过自动打印');
          ElMessage.warning(`订单 ${bussId} 不满足自动打印条件，请手动处理`);
        }
      }
    } catch (error) {
      console.error('自动打印过程中出错:', error);
      ElMessage.error(`订单 ${bussId} 自动打印失败`);
    }
  }

  /**
   * 安排重连
   */
  private scheduleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('MQTT重连次数已达上限，停止重连');
      ElMessage.error('MQTT服务连接失败，请检查网络后手动刷新页面');
      return;
    }

    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
    }

    const delay = this.reconnectInterval * Math.pow(2, this.reconnectAttempts); // 指数退避
    this.reconnectAttempts++;

    console.log(`${delay / 1000}秒后尝试第${this.reconnectAttempts}次重连...`);

    this.reconnectTimer = setTimeout(async () => {
      if (!this.isConnected && this.connectionConfig) {
        console.log(`开始第${this.reconnectAttempts}次重连尝试...`);
        await this.connect();
      }
    }, delay);
  }

  /**
   * 监听用户状态变化
   */
  private setupUserStateWatcher() {
    // 监听用户登录状态
    this.userStore.$subscribe((mutation, state) => {
      if (!state.userInfo) {
        // 用户退出登录，断开MQTT连接
        console.log('用户退出登录，断开MQTT连接');
        this.disconnect();
      }
    });
  }

  /**
   * 播放语音通知
   */
  private playVoiceNotification(message: string) {
    try {
      if (this.printStore.globalPrintSettings.sound === 'play') {
        // 使用浏览器的语音合成API
        if ('speechSynthesis' in window) {
          const utterance = new SpeechSynthesisUtterance(message);
          utterance.lang = 'zh-CN';
          utterance.rate = 1;
          utterance.pitch = 1;
          speechSynthesis.speak(utterance);
        }
      }
    } catch (error) {
      console.error('播放语音通知失败:', error);
    }
  }

  /**
   * 获取连接状态
   */
  public getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      reconnectAttempts: this.reconnectAttempts,
      maxReconnectAttempts: this.maxReconnectAttempts,
      config: this.connectionConfig ? {
        clientId: this.connectionConfig.clientId,
        username: this.connectionConfig.username,
        topic: this.subscriptionTopic,
        host: mqttConfig.host,
        port: mqttConfig.port
      } : null
    };
  }

  /**
   * 手动重连
   */
  public async reconnect(): Promise<boolean> {
    console.log('手动触发MQTT重连...');
    this.reconnectAttempts = 0; // 重置重连次数
    return await this.connect();
  }
}

// 创建单例实例
export const mqttService = new MqttService();




