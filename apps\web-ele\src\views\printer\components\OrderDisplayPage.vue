<template>
  <div class="order-display-page">
    <div class="preview-container">
      <FilePreviewer />
    </div>
    <div class="details-container">
      <FileDetails/>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, defineProps } from 'vue';
import type { PropType } from 'vue';
import FilePreviewer from '../components/FilePreviewer.vue';
import FileDetails from '../components/FileDetails.vue';
</script>

<style scoped lang="scss">
.order-display-page {
  display: flex;
  flex-direction: column;
  height: 93vh;
  background-color: #1F2937; /* 整体页面背景 */
  padding: 16px;
  gap: 16px;
  border-radius: 8px;
}

.preview-container {
  flex-shrink: 0;
  height: 60%; /* 预览区占45%高度 */
}

.details-container {
  flex-grow: 1;
  min-height: 0; /* flex 布局技巧 */
}
</style>
