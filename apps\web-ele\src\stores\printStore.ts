// src/stores/printStore.ts

import { defineStore } from 'pinia';
import { computed, ref } from 'vue';
import { ElMessage } from 'element-plus';
import { useUserStore } from '@vben/stores';
import dayjs from 'dayjs';
import {
  getReadyPrintFiles,
  printFileReport,
  shopAddPrinter,
  shopDelPrinter,
  shopUpPrinter,
  orderConfirm,
  printOrderStart
} from '#/api/wosys/printer';

import * as printerService from '#/services/printerConfigService';

import type {
  Order,
  PrintFile,
  Printer as PrinterType,
  PrinterCapability,
  PrintJob,
  PrintStatusData,
} from '#/types/print.types';

// 创建并导出 Store
export const usePrintStore = defineStore('print', () => {
  // =================================================================
  // 1. State (状态)
  // =================================================================
  // 订单列表
  const orderList = ref<ApiOrder[]>([]);
  // 加载状态
  const isLoading = ref(true);
  // 选中得订单
  const selectedOrderId = ref<string | null>(null);
  // 选中得文件
  const selectedFilesForPrint = ref<PrintFile[]>([]);

  const activePrinter = ref<Printer | null>(null);
  // 当前预览得文件
  const activePreviewFile = ref<PreviewData | null>(null);
   // 打印队列状态
  const printQueue = ref<PrintJob[]>([]);
  // ：历史打印记录队列
  const printHistory = ref<PrintJob[]>([]);
  // 打印机得列表
  const printerCapabilities = ref<PrinterCapability[]>(
    printerService.getStoredPrinterConfigs(),
  );

  // 存储物理打印机及其状态
  const physicalPrinters = ref<Printer[]>([]);
  // 全局打印设置
  const globalPrintSettings = ref({
    mode: 'auto' as 'auto' | 'manual',
    sound: 'play' as 'play' | 'mute',
    orderPageMode: 'byPrinter' as 'byCopies' | 'byPrinter',
    downloadDirectory: '' as string // 下载目录设置，空字符串表示使用默认目录
  });

  // 保存全局打印设置
  function saveGlobalPrintSettings(settings: typeof globalPrintSettings.value) {
    globalPrintSettings.value = { ...settings };
    localStorage.setItem('vben_global_print_settings', JSON.stringify(settings));
  }

  // 加载全局打印设置
  function loadGlobalPrintSettings() {
    const saved = localStorage.getItem('vben_global_print_settings');
    if (saved) {
      try {
        globalPrintSettings.value = JSON.parse(saved);
      } catch (error) {
        console.error('加载打印设置失败:', error);
      }
    }
  }

  // 语音播报功能
  function playVoiceNotification(message: string) {
    if (globalPrintSettings.value.sound === 'play') {
      // 使用浏览器的语音合成API
      if ('speechSynthesis' in window) {
        const utterance = new SpeechSynthesisUtterance(message);
        utterance.lang = 'zh-CN';
        utterance.rate = 1;
        utterance.pitch = 1;
        speechSynthesis.speak(utterance);
      }
    }
  }

  // 检查订单是否全部完成并更新状态 - 新增队列转移逻辑
  function checkAndUpdateOrderStatus() {
    if (!selectedOrder.value) return;

    const currentOrderFiles = selectedOrder.value.printFileList;
    if (!currentOrderFiles || currentOrderFiles.length === 0) return;

    const allCompleted = currentOrderFiles.every(file => file.status === '1');
    const hasFailure = currentOrderFiles.some(file => file.status === '2');

    if (allCompleted) {
      // 所有文件打印成功，更新订单状态为打印完成
      const orderIndex = orderList.value.findIndex(order => order.bussId === selectedOrder.value?.bussId);
      if (orderIndex !== -1) {
        orderList.value[orderIndex].status = '5'; // 打印完成

        // 从订单列表中移除已完成的订单（仅在打印订单tab中，暂存订单tab不移除）
        if (orderList.value[orderIndex].status === '5') {
          orderList.value.splice(orderIndex, 1);

          // 如果当前选中的订单被移除，选择下一个订单或清空预览
          if (selectedOrderId.value === selectedOrder.value?.bussId) {
            if (orderList.value.length > 0) {
              selectOrder(orderList.value[0].bussId);
            } else {
              selectedOrderId.value = null;
              activePreviewFile.value = null; // 清空预览文件
            }
          }
        }
      }

      // 将当前订单的打印任务转移到历史记录
      moveCompletedJobsToHistory();

      playVoiceNotification('订单打印完成');
      ElMessage.success('订单所有文件打印完成！');
    } else if (hasFailure) {
      // 有文件打印失败，更新订单状态为打印失败
      const orderIndex = orderList.value.findIndex(order => order.bussId === selectedOrder.value?.bussId);
      if (orderIndex !== -1) {
        orderList.value[orderIndex].status = '6'; // 打印失败
      }
    }
  }

  // 初始化时加载设置
  loadGlobalPrintSettings();

  // =================================================================
  // 2. Getters (计算属性)
  // =================================================================
  const selectedOrder = computed(() => {
    if (!selectedOrderId.value) return null;
    return orderList.value.find(
      (order) => order.bussId === selectedOrderId.value,
    );
  });


  // 验证是否已经认领任务
  const isClaimOrder = computed(() =>{
    // if (!selectedOrderId.value) return null;
    let status = selectedOrder.value?.status;
    if(status  != '1') {
      return true;
    }
    return false;
  });

  const filesForDisplay = computed((): PrintFile[] => {
    if (!selectedOrder.value?.printFileList) return [];
    const res =  selectedOrder.value.printFileList
      .map((apiFile, index) => ({
        id: index + 1,
        fileId: apiFile.printFileId,
        fileKey: apiFile.fileUrl.split("/").pop(),
        fileName: apiFile.originFileName || `照片-${index + 1}`,
        copies: apiFile.printTimes || 1,
        size: apiFile.paperSize || 'A4',
        range: '部分',
        pageTotal: apiFile.pageTotal || 1,
        startPageNumber: apiFile.startPageNumber || 1,
        endPageNumber: apiFile.endPageNumber || apiFile.pageTotal || 1,
        duplex: apiFile.printPage === 'DOUBLE' ? '双面' : '单面',
        color: apiFile.printColor === 'BLACKWHITE' ? '黑白' : '标准彩印',
        paper: apiFile.paperKind === 'UPSCALE' ? '高光' : '普通',
        status: apiFile.status || '0',
        fileUrl: apiFile.fileUrl,
        fileType: apiFile.fileType,
      }));
      console.log('filesForDisplay', res);
      return res;

  });

  const summaryForDisplay = computed(() => ({
    total: filesForDisplay.value.length,
    processing: 0,
    completed: filesForDisplay.value.length,
  }));

  // =================================================================
  // 3. Actions (方法)
  // =================================================================
  const userStore = useUserStore();

  // 添加当前tab状态
  const activeTabName = ref('1');

  // 认领任务
  async function claimOrder(confirmType: string = '3') {
    console.log(filesForDisplay.value);
    let printPlan = findAvailablePrintersForOrder(filesForDisplay.value);
    if(printPlan == false) {
      ElMessage.warning('打印机不满足认领任务得要求');
      return;
    }else {
      await orderConfirm({bussId: selectedOrderId.value , employeeId: userStore.userInfo?.employeeId , confirmType});

      // 直接更新本地订单状态
      const orderIndex = orderList.value.findIndex(order => order.bussId === selectedOrderId.value);
      if (orderIndex !== -1) {
        orderList.value[orderIndex].status = confirmType === '3' ? '3' : '2';
      }

      ElMessage.success(`成功认领任务 ${selectedOrderId.value}`);
      return;
    }
  }

  // 获取订单列表 - 支持历史订单搜索参数
  async function fetchOrders(queryType: number = 1, pagination = { pageNum: 1, pageSize: 10 }, resetList = false) {
    try {
      if (resetList) {
        isLoading.value = true;
      }

      const response = await getReadyPrintFiles({
        queryType,
        shopId: userStore.userInfo?.shopId,
        ...pagination,
      });
      console.log('response', response);
      const newOrders = response?.list || [];

      // 添加数据验证，防止无效数据导致无限滚动
      if (!Array.isArray(newOrders)) {
        console.warn('获取到的订单数据格式不正确:', newOrders);
        return [];
      }

      if (resetList) {
        console.log('追加新订单数据:', newOrders);
        // 重置列表
        orderList.value = newOrders;
      } else {
        // 如果没有新数据，停止加载更多
        if (newOrders.length === 0) {
          console.log('没有更多订单数据');
          return [];
        }

        // 追加到列表 - 去重处理
        const existingIds = new Set(orderList.value.map(order => order.bussId));
        const filteredNewOrders = newOrders.filter(order => !existingIds.has(order.bussId));

        if (filteredNewOrders.length === 0) {
          console.log('没有新的订单数据');
          return [];
        }

        const sortedNewOrders = filteredNewOrders.sort((a, b) => {
          const aStatus = parseInt(a.status);
          const bStatus = parseInt(b.status);
          const aClaimed = aStatus > 1 ? 1 : 0;
          const bClaimed = bStatus > 1 ? 1 : 0;
          return bClaimed - aClaimed;
        });

        orderList.value.push(...sortedNewOrders);
      }

      return newOrders;
    } catch (error) {
      console.error('获取订单数据失败', error);
      ElMessage.error('获取打印任务失败，请重试');
      return [];
    } finally {
      if (resetList) {
        isLoading.value = false;
      }
    }
  }

  // 用于选择文件并更新预览状态的方法
  function selectFileForPreview(file: PrintFile) {
    console.log('Store: 正在更新预览文件 ->', file.fileName);
    activePreviewFile.value = {
      type: file.fileType,
      url: file.fileUrl,
    };
  }

  // 选中订单
  function selectOrder(orderId: string) {
    selectedOrderId.value = orderId;
    selectedFilesForPrint.value = []; // 清空文件勾选

    // 当切换订单时，如果新订单有文件，则自动预览第一个文件
    if (filesForDisplay.value.length > 0) {
      selectFileForPreview(filesForDisplay.value[0]);
    } else {
      activePreviewFile.value = null; // 如果没文件，则清空预览
    }
  }

  // 选择打印机
  function selectPrinter(printer: Printer) {
    activePrinter.value = printer;
    ElMessage.success(`已选择打印机: ${printer.name}`);
  }

  function updateFileSelection(files: PrintFile[]) {
    // 过滤掉已举报的文件，防止它们被选中
    const validFiles = files.filter(file => file.status !== '3');
    selectedFilesForPrint.value = validFiles;

    // 如果有文件被过滤掉，提示用户
    if (files.length > validFiles.length) {
      ElMessage.warning('已举报的文件无法选择进行打印');
    }
  }

  /**
   * 添加一个新的打印机配置
   * @param config - 要添加的配置
   */
  function addPrinterCapability(
    config: Omit<PrinterCapability, 'id'>,
  ): boolean {
    // 验证：打印机名称是否已存在
    const nameExists = printerCapabilities.value.some(
      (p) => p.printerName === config.printerName,
    );
    if (nameExists) {
      ElMessage.error(
        `添加失败：打印机 [${config.printerName}] 的配置已存在！`,
      );
      return false;
    }

    const newConfig: PrinterCapability = {
      ...config,
      id: `config_${Date.now()}`, // 生成唯一ID
    };

    printerCapabilities.value.push(newConfig);
    printerService.savePrinterConfigs(printerCapabilities.value); // 持久化到LocalStorage
    ElMessage.success(`配置 [${config.alias}] 已成功添加！`);
    return true;
  }

  /**
   * 修改一个已有的打印机配置
   * @param updatedConfig - 更新后的配置
   */
  function updatePrinterCapability(updatedConfig: PrinterCapability): boolean {
    // 验证：新的打印机名称是否与其他配置冲突
    const nameExists = printerCapabilities.value.some(
      (p) =>
        p.id !== updatedConfig.id &&
        p.printerName === updatedConfig.printerName,
    );
    if (nameExists) {
      ElMessage.error(
        `修改失败：打印机 [${updatedConfig.printerName}] 的配置已存在！`,
      );
      return false;
    }

    const index = printerCapabilities.value.findIndex(
      (p) => p.id === updatedConfig.id,
    );
    if (index !== -1) {
      printerCapabilities.value[index] = updatedConfig;
      printerService.savePrinterConfigs(printerCapabilities.value); // 持久化
      ElMessage.success(`配置 [${updatedConfig.alias}] 已成功更新！`);
      return true;
    }
    return false;
  }

  /**
   * 删除一个打印机配置
   * @param configId - 要删除的配置ID
   */
  async function deletePrinterCapability(configId: string) {
    try {
      await ElMessageBox.confirm(
        '确定要删除这个打印机配置吗？此操作不可撤销。',
        '确认删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        },
      );

      printerCapabilities.value = printerCapabilities.value.filter(
        (p) => p.id !== configId,
      );
      printerService.savePrinterConfigs(printerCapabilities.value); // 持久化
      ElMessage.success('打印机配置已删除。');
    } catch (error) {
      // 用户点击了“取消”
      ElMessage.info('已取消删除操作。');
    }
  }

  /**
   *  检查单个文件是否能被某个打印机能力配置所满足
   * @param file - 要打印的文件
   * @param capability - 打印机能力配置
   * @returns boolean
   */
  function isFileSupported(
    file: PrintFile,
    capability: PrinterCapability,
  ): boolean {
    const isPaperSizeOk = capability.paperSizes.includes(file.size);
    const isDuplexOk = capability.duplexModes.includes(
      file.duplex === '单面' ? 'SINGLE' : 'DOUBLE',
    );
    const isColorOk = capability.colorModes.includes(
      file.color === '黑白' ? 'BLACKWHITE' : 'COLOR',
    );
    const isTypeOk = capability.supportedTypes.includes(file.fileType);
    // 版式可以根据需要添加更复杂的逻辑
    const isLayoutOk = true;

    const isStatusOk = (capability.status == 'enabled') ? true : false;

    return isPaperSizeOk && isDuplexOk && isColorOk && isTypeOk && isLayoutOk && isStatusOk;
  }
  /**
   * 为整个订单寻找可用的打印机
   * @param orderFiles - 订单中的所有文件
   * @returns 返回所有能满足该订单的打印机配置列表
   */
  function findAvailablePrintersForOrder(
    filesToPrint: PrintFile[],
  ){
    if (filesToPrint.length === 0) {
      ElMessage.warning('请至少选择一个要打印的文件。');
      return;
    }

    // 1. 尝试“最优策略”：寻找一台能打印所有文件的打印机
    const capablePrintersForAll = printerCapabilities.value.filter(
      (capability) => {
        return filesToPrint.every((file) => isFileSupported(file, capability));
      },
    );

    let printPlan: { file: PrintFile; printer: PrinterCapability }[] = [];

    if (capablePrintersForAll.length > 0) {
      // 策略1：找到“全能打印机”，所有文件都用它打印
      const chosenPrinter = capablePrintersForAll[0]; // 使用找到的第一个
      ElMessage.success(
        `智能匹配成功！将使用打印机 [${chosenPrinter.printerName}] 打印所有文件。`,
      );
      printPlan = filesToPrint.map((file) => ({
        file,
        printer: chosenPrinter,
      }));
    } else {
      // 策略2：未找到“全能打印机”，尝试为每个文件单独寻找打印机（智能分流）
      ElMessage.info('未找到单台可满足所有要求的打印机，正在尝试智能分流...');

      const tempPlan: { file: PrintFile; printer: PrinterCapability }[] = [];
      for (const file of filesToPrint) {
        const capablePrintersForFile = printerCapabilities.value.filter((p) =>
          isFileSupported(file, p),
        );
        if (capablePrintersForFile.length === 0) {
          // 失败安全机制：只要有一个文件找不到打印机，就中止整个任务
          ElMessage.error(
            `打印任务失败！文件 [${file.fileName}] 找不到任何可以满足其打印要求的打印机。`,
          );
          return false; // 中断
        }
        // 分流策略：为该文件分配找到的第一个可用打印机
        tempPlan.push({ file: file, printer: capablePrintersForFile[0] });
      }
      printPlan = tempPlan;
      ElMessage.success('智能分流成功！已为每个文件分配好打印机。');
    }

    return printPlan;
  }

  /**
   * 核心打印逻辑
   * @param file - 要打印的单个文件
   * @param printer - 用于打印的打印机配置
   * @param copies - 打印份数
   * @param showPreview - 是否显示预览（封面打印时为true）
   */
  async function executePrintJob(file: PrintFile, printer: PrinterCapability, copies: number = 1, showPreview: boolean = false) {
    const jobKey = file.fileKey;

    // 检查是否已在队列中，但允许失败的任务重新打印
    const existingJob = printQueue.value.find(job => job.jobKey === jobKey);
    if (existingJob) {
      if (existingJob.status === 'queuing') {
        ElMessage.warning(`文件 [${file.fileName}] 已在打印队列中。`);
        return;
      } else if (existingJob.status === 'failed') {
        const index = printQueue.value.findIndex(job => job.jobKey === jobKey);
        if (index !== -1) {
          printQueue.value.splice(index, 1);
        }
        ElMessage.info(`正在重新打印失败的文件 [${file.fileName}]`);
      } else if (existingJob.status === 'completed') {
        ElMessage.warning(`文件 [${file.fileName}] 已打印完成。`);
        return;
      }
    }

    const newJob: PrintJob = {
      jobKey: jobKey,
      fileId: file.fileId,
      file: file,
      status: 'queuing',
      printerName: printer.printerName,
      timestamp: new Date().toLocaleTimeString(),
    };
    printQueue.value.unshift(newJob);

    // 更新文件状态为打印中
    updateFileStatus(jobKey, '3');

    try {
      if(selectedOrder.value?.status == '2' || selectedOrder.value?.status == '3'){
        await printOrderStart({bussId: selectedOrder.value.bussId})
      }

      // 增加延迟
      await new Promise(resolve => setTimeout(resolve, 800));

      // 每次都创建新的参数对象，避免引用问题
      const paperBin = file.fileType === '4'
        ? (printer.orderPageBin || 'auto')
        : (printer.paperBins?.[file.size] || 'auto');

      const printOptions = {
        printerName: String(printer.printerName),
        filePath: String(file.fileUrl),
        colorMode: file.color === '黑白' ? 'monochrome' : 'color',
        duplexMode: file.duplex === '双面' ? 'duplexlong' : 'simplex',
        paperSize: String(file.size),
        binName: String(paperBin),
        copies: Number(copies),
        show: Boolean(showPreview),
      };

      console.log(`发送打印任务 [${jobKey}] 参数:`, JSON.stringify(printOptions, null, 2));

      await aardio.printPdf(printOptions);
      console.log(`任务 [${jobKey}] 已成功发送给 aardio。`);
    } catch (error) {
      console.error(`任务 [${jobKey}] 发送失败:`, error);
      updateJobStatus(jobKey, false, '发送到打印程序失败');
    }
  }

  // 更新文件状态的方法 - 修复切换订单后状态更新问题
  function updateFileStatus(fileKey: string, status: '0' | '1' | '2' | '3') {
    // 在所有订单中查找对应的文件，而不仅仅是当前选中的订单
    const targetOrder = orderList.value.find(order =>
      order.printFileList.some(file => file.fileUrl.split("/").pop() === fileKey)
    );

    if (!targetOrder) return;

    const fileIndex = targetOrder.printFileList.findIndex(file =>
      file.fileUrl.split("/").pop() === fileKey
    );
    if (fileIndex !== -1) {
      // 直接更新API数据结构中的状态
      targetOrder.printFileList[fileIndex].status = status;
      console.log(`更新文件状态: ${fileKey} -> ${status} (订单: ${targetOrder.bussId})`);

      // 如果文件打印完成，检查是否需要从当前列表移除订单
      if (status === '1') {
        // 检查该订单的所有文件是否都已完成
        const allFilesCompleted = targetOrder.printFileList.every(file => file.status === '1');

        if (allFilesCompleted) {
          // 更新订单状态为打印完成
          targetOrder.status = '5';

          // 如果当前在打印订单tab(1)或异常订单tab(3)，且是当前选中的订单，则移除
          if ((activeTabName.value === '1' || activeTabName.value === '3') &&
              selectedOrderId.value === targetOrder.bussId) {

            const orderIndex = orderList.value.findIndex(order => order.bussId === targetOrder.bussId);
            if (orderIndex !== -1) {
              orderList.value.splice(orderIndex, 1);
              console.log(`订单 ${targetOrder.bussId} 已从${activeTabName.value === '1' ? '打印订单' : '异常订单'}列表中移除`);

              // 选择下一个订单或清空预览
              if (orderList.value.length > 0) {
                const nextOrder = orderList.value[Math.min(orderIndex, orderList.value.length - 1)];
                selectOrder(nextOrder.bussId);
              } else {
                selectedOrderId.value = null;
                activePreviewFile.value = null;
              }
            }
          }

          playVoiceNotification('订单打印完成');
          ElMessage.success(`订单 ${targetOrder.bussId} 所有文件打印完成！`);
        }
      }
    }
  }

  // 检查并更新订单状态 - 修复切换订单后状态更新问题
  function checkAndUpdateOrderStatus() {
    // 检查所有订单的状态，而不仅仅是当前选中的订单
    orderList.value.forEach(order => {
      const currentOrderFiles = order.printFileList;
      const allCompleted = currentOrderFiles.every(file => file.status === '1');
      const hasFailure = currentOrderFiles.some(file => file.status === '2');

      if (allCompleted && order.status !== '5') {
        // 所有文件打印成功，更新订单状态为打印完成
        order.status = '5';

        // 如果是当前选中的订单且不在暂存订单tab中，从列表中移除
        if (selectedOrderId.value === order.bussId && activeTabName.value !== '2') {
          const orderIndex = orderList.value.findIndex(o => o.bussId === order.bussId);
          if (orderIndex !== -1) {
            orderList.value.splice(orderIndex, 1);

            // 选择下一个订单或清空预览
            if (orderList.value.length > 0) {
              selectOrder(orderList.value[0].bussId);
            } else {
              selectedOrderId.value = null;
              activePreviewFile.value = null;
            }
          }
        }

        playVoiceNotification('订单打印完成');
        ElMessage.success(`订单 ${order.bussId} 所有文件打印完成！`);
      } else if (hasFailure && order.status !== '6') {
        // 有文件打印失败，更新订单状态为打印失败
        order.status = '6';
      }
    });

    // 将当前订单的打印任务转移到历史记录
    moveCompletedJobsToHistory();
  }

  /**
   *  更新队列中任务的状态
   * @param jobKey - 任务的唯一标识
   * @param success - 打印是否成功
   * @param message - 失败时的错误信息
   */
  async function updateJobStatus(jobKey: string, success: boolean, message?: string) {
    const jobIndex = printQueue.value.findIndex(j => j.jobKey === jobKey);
    if (jobIndex === -1) return;

    const currentJob = printQueue.value[jobIndex];

    if (success) {
      currentJob.status = 'completed';
      updateFileStatus(jobKey, '1');
      // playVoiceNotification('文件打印成功');
      ElMessage.success(`文件 [${currentJob.file.fileName}] 打印成功！`);
    } else {
      currentJob.status = 'failed';
      currentJob.errorMessage = message;
      updateFileStatus(jobKey, '2');
      playVoiceNotification('文件打印失败');
      ElMessage.error(`文件 [${currentJob.file.fileName}] 打印失败: ${message}`);
    }

    // 立即将完成的任务转移到历史记录（不管成功还是失败）
    moveCompletedJobsToHistory();

    // 检查并更新订单状态
    checkAndUpdateOrderStatus();

    // 通知后端 - 但如果是订单页且已经成功过，则不重复上报
    const shouldReportToBackend = !(currentJob.file.fileType === '4' && success && isOrderPageAlreadyReported(currentJob.file.fileId));

    if (shouldReportToBackend) {
      try {
        await printFileReport({
          printFileId: currentJob.file.fileId,
          printResult: currentJob.status == 'completed' ? '1' : '2',
          printResultDesc: currentJob.status == 'completed' ? '打印成功' : `打印失败: ${message}`,
          printResultTime: dayjs().format('YYYY-MM-DD HH:mm:ss')
        });

        // 如果是订单页且打印成功，记录已上报状态
        if (currentJob.file.fileType === '4' && success) {
          markOrderPageAsReported(currentJob.file.fileId);
        }
      } catch (error) {
        console.error('通知后端打印结果失败:', error);
      }
    } else {
      console.log(`订单页 [${currentJob.file.fileName}] 已成功上报过，跳过后端通知`);
    }
  }

  /**
   * 设置一个全局监听器，供 aardio 回调
   * 这个函数应该在应用初始化时被调用一次
   */
  function setupExternalListener() {
    // 确保 window.app 对象存在
    window.app = window.app || {};

    // 定义回调函数
    window.app.reportPrintStatus = (statusUpdate) => {
      console.log('接收到来自外部程序的打印状态回报:', statusUpdate);
      const { fileKey, success, message, status } = statusUpdate;

      // 添加安全检查
      if (!fileKey) {
        console.warn('收到的状态更新缺少 fileKey');
        return;
      }

      if(status == '打印成功') {
        updateJobStatus(fileKey, true, message);
      } else if(status == '打印失败'){
        updateJobStatus(fileKey, false, message);
      }
      // 其他情况暂时不上报
    };
    console.log("外部打印状态监听器和MQTT订单监听器已设置。");
  }

  // 打印订单页
  async function printOrderPage(printer: PrinterCapability) {
    if (!selectedOrder.value) return;

    // 从订单文件列表中找到订单页文件（fileType === '4'）
    const orderPageFile = selectedOrder.value.printFileList.find(file => file.fileType === '4');
    if (!orderPageFile) {
      console.log('当前订单没有订单页文件');
      return;
    }

    // 转换为 PrintFile 格式
    const printFile: PrintFile = {
      id: 0,
      fileId: orderPageFile.printFileId,
      fileKey: orderPageFile.fileUrl.split("/").pop() || '',
      fileName: orderPageFile.originFileName || '订单页',
      copies: 1, // 订单页固定打印1份
      size: orderPageFile.paperSize || 'A4',
      range: '全部',
      duplex: orderPageFile.printPage === 'DOUBLE' ? '双面' : '单面',
      color: orderPageFile.printColor === 'BLACKWHITE' ? '黑白' : '标准彩印',
      paper: orderPageFile.paperKind === 'UPSCALE' ? '高光' : '普通',
      status: orderPageFile.status || '0',
      fileUrl: orderPageFile.fileUrl,
      fileType: '4',
    };

    // 使用 executePrintJob 方法来处理订单页打印，这样可以统一处理状态上报
    await executePrintJob(printFile, printer, 1, false);
  }

  /**
   * 专门用于打印封面的方法
   * @param file - 封面文件
   */
  async function printCoverFile(file: PrintFile) {
    if (file.fileType !== '2') {
      ElMessage.error('该文件不是封面类型，无法使用封面打印功能');
      return;
    }

    const printPlan = findAvailablePrintersForOrder([file]);
    if (printPlan === false) return;

    const { printer } = printPlan[0];
    await executePrintJob(file, printer, file.copies, true); // showPreview = true
    ElMessage.success('封面文件已发送到打印队列（预览模式）！');
  }

  /**
   * 过滤文件 - 根据是否包含订单页来过滤文件
   * @param files - 要过滤的文件列表
   * @param includeOrderPage - 是否包含订单页
   * @returns 过滤后的文件列表
   */
  function filterFilesForPrint(files: PrintFile[], includeOrderPage: boolean = true, yesNoSelect: boolean = false): PrintFile[] {
    return files.filter(file => {
      // 已举报的文件不参与任何打印操作
      if (file.status === '3') {
        return false;
      }

      // 封面类型永远不参与批量打印
      if (file.fileType === '2') {
        return false;
      }

      // 订单页类型
      if (file.fileType === '4') {
        if(includeOrderPage == true) {
          return false;
        }
        if(yesNoSelect == false) {
          return false;
        }
      }

      return true;
    });
  }

  // 修改智能打印逻辑 - 增加文件过滤
  async function executeSmartPrint(filesToPrint: PrintFile[], includeOrderPage: boolean = true , showPreview: boolean = false ,yesNoSelect: boolean = false) {
    // 过滤文件
    const filteredFiles = filterFilesForPrint(filesToPrint, includeOrderPage , yesNoSelect);

    if (filteredFiles.length === 0) {
      ElMessage.warning('没有可打印的文件');
      return;
    }

    let printPlan = findAvailablePrintersForOrder(filteredFiles);
    if (printPlan == false) return true;

    // 按打印机分组
    const printerGroups = new Map();
    printPlan.forEach(item => {
      const printerName = item.printer.printerName;
      if (!printerGroups.has(printerName)) {
        printerGroups.set(printerName, { printer: item.printer, files: [] });
      }
      printerGroups.get(printerName).files.push(item.file);
    });

    for (const [printerName, group] of printerGroups) {
      const { printer, files } = group;
      const orderPageMode = globalPrintSettings.value.orderPageMode;

      if (orderPageMode === 'byCopies' && includeOrderPage) {
        // 按份数打印：每打印一份文件就打印一个订单页
        for (const file of files) {
          for (let i = 0; i < file.copies; i++) {
            // 确保每次调用都传递完整的参数
            await executePrintJob(file, printer, 1, showPreview);
            await new Promise(resolve => setTimeout(resolve, 1500)); // 增加延迟到1.5秒
            // 每份文件后打印一个订单页
            await printOrderPage(printer);
            await new Promise(resolve => setTimeout(resolve, 1500));
          }
        }
      } else {
        // 按打印机打印：先打印所有文件，再打印订单页
        for (const file of files) {
          // 确保每次调用都传递完整的参数
          await executePrintJob(file, printer, file.copies, showPreview);
          await new Promise(resolve => setTimeout(resolve, 1500)); // 增加延迟到1.5秒
        }
        // 所有文件打印完后，只打印一个订单页
        if (includeOrderPage && orderPageMode === 'byPrinter') {
          await printOrderPage(printer);
        }
      }
    }

    ElMessage.success('所有选定的文件已成功发送到打印队列！');
  }

  // 新增打印方法
  async function printAllWithoutOrderPage() {
    await executeSmartPrint(filesForDisplay.value, false, false , false);
  }

  async function printAllWithOrderPage() {
    await executeSmartPrint(filesForDisplay.value, true, false , false);
  }

  async function printSelectedFiles() {
    await executeSmartPrint(selectedFilesForPrint.value, false , false , true);
  }

  // 打印单个文件 - 修改为支持封面检测和订单页处理
  async function printSingleFile(file: PrintFile , showPreview: boolean = false) {
    // 检查文件是否已被举报，如果是则不允许打印
    if (file.status === '3') {
      ElMessage.warning('已举报的文件无法打印');
      return;
    }

    if (file.fileType === '2') {
      // 如果是封面，使用专门的封面打印方法
      await printCoverFile(file);
    } else if (file.fileType === '4') {
      // 如果是订单页，直接作为普通文件打印（不触发订单页逻辑）
      const printPlan = findAvailablePrintersForOrder([file]);
      if (printPlan === false) return;

      const { printer } = printPlan[0];
      await executePrintJob(file, printer, file.copies, showPreview);
    } else {
      // 其他类型正常打印，但不包含订单页逻辑
      await executeSmartPrint([file], false, showPreview);
    }
  }

  // 批量打印
  async function batchPrint() {
    await executeSmartPrint(selectedFilesForPrint.value);
  }


  // =================================================================
  // 4. 返回 state, getters, 和 actions
  // =================================================================
  // 将完成的任务转移到历史记录
  function moveCompletedJobsToHistory() {
    if (!selectedOrder.value) return;

    // 找到当前订单的所有打印任务
    const currentOrderJobs = printQueue.value.filter(job => {
      return selectedOrder.value.printFileList.some(file =>
        file.fileUrl.split("/").pop() === job.jobKey
      );
    });

    // 找到所有已完成的任务（成功或失败）
    const completedJobs = currentOrderJobs.filter(job =>
      job.status === 'completed' || job.status === 'failed'
    );

    if (completedJobs.length > 0) {
      // 将完成的任务添加到历史记录
      printHistory.value.unshift(...completedJobs);

      // 从当前队列中移除这些任务
      printQueue.value = printQueue.value.filter(job =>
        !completedJobs.some(completedJob => completedJob.jobKey === job.jobKey)
      );

      console.log(`已将 ${completedJobs.length} 个完成的打印任务转移到历史记录`);
    }

    // 检查是否所有任务都已完成
    const remainingJobs = printQueue.value.filter(job => {
      return selectedOrder.value.printFileList.some(file =>
        file.fileUrl.split("/").pop() === job.jobKey
      );
    });

    if (remainingJobs.length === 0 && currentOrderJobs.length > 0) {
      console.log(`订单 ${selectedOrder.value.bussId} 的所有打印任务已完成并转移到历史记录`);
    }
  }

  // ：获取合并后的打印日志用于显示
  const displayPrintLogs = computed(() => {
    return [...printQueue.value, ...printHistory.value];
  });

  // ：清理历史记录的方法
  function clearPrintHistory() {
    printHistory.value = [];
    ElMessage.success('历史打印记录已清空');
  }
    // 删除正在执行的任务
  function cleardisplayPrintHistory() {
    printQueue.value = [];
    ElMessage.success('历史打印记录已清空');
  }

  // 确保有正确的 $reset 方法
  function $reset() {
    // 重置所有状态到初始值
    orderList.value = [];
    selectedOrderId.value = null;
    activePreviewFile.value = null;
    printQueue.value = [];
    printHistory.value = [];
    globalPrintSettings.value = {
      mode: 'auto',
      sound: 'play',
      orderPageMode: 'byCopies',
      downloadDirectory: ''
    };
    printerCapabilities.value = [];
  }

  // 记录已上报的订单页
  const reportedOrderPages = ref<Set<string>>(new Set());

  function isOrderPageAlreadyReported(fileId: string): boolean {
    return reportedOrderPages.value.has(fileId);
  }

  function markOrderPageAsReported(fileId: string) {
    reportedOrderPages.value.add(fileId);
  }

  // 获取MQTT连接状态
  function getMqttStatus() {
    try {
      const { mqttService } = require('#/services/mqttService');
      return mqttService.getConnectionStatus();
    } catch (error) {
      console.error('获取MQTT状态失败:', error);
      return {
        isConnected: false,
        reconnectAttempts: 0,
        maxReconnectAttempts: 5,
        config: null
      };
    }
  }

  // 手动重连MQTT
  async function reconnectMqtt() {
    try {
      const { mqttService } = await import('#/services/mqttService');
      return await mqttService.reconnect();
    } catch (error) {
      console.error('MQTT重连失败:', error);
      return false;
    }
  }

  // 发布打印状态到MQTT
  async function publishPrintStatus(bussId: string, status: string, message?: string) {
    try {
      const { mqttService } = await import('#/services/mqttService');
      const { mqttConfig } = await import('#/config/mqtt');

      const userInfo = userStore.userInfo;
      if (!userInfo?.shopId || !userInfo?.employeeId) return;

      const statusTopic = mqttConfig.generateStatusTopic(userInfo.shopId, userInfo.employeeId);
      const statusMessage = {
        bussId,
        status,
        message: message || '',
        timestamp: new Date().toISOString(),
        shopId: userInfo.shopId,
        employeeId: userInfo.employeeId
      };

      await mqttService.publish(statusTopic, statusMessage);
      console.log('打印状态已上报:', statusMessage);

    } catch (error) {
      console.error('上报打印状态失败:', error);
    }
  }

  return {
    // State
    orderList,
    isLoading,
    selectedOrderId,
    activePrinter,
    activePreviewFile,
    isClaimOrder,
    globalPrintSettings,
    saveGlobalPrintSettings,
    loadGlobalPrintSettings,
    checkAndUpdateOrderStatus,
    // Getters
    selectedOrder,
    filesForDisplay,
    summaryForDisplay,
    printQueue,
    printHistory, // 新增
    displayPrintLogs, // ：用于显示的合并日志
    // Actions
    fetchOrders,
    selectOrder,
    selectPrinter,
    updateFileSelection,
    printSingleFile,
    batchPrint,
    selectFileForPreview,
    printerCapabilities,
    addPrinterCapability,
    updatePrinterCapability,
    deletePrinterCapability,
    claimOrder,
    setupExternalListener,
    printAllWithoutOrderPage,
    printAllWithOrderPage,
    printSelectedFiles,
    updateFileStatus,
    clearPrintHistory, // 新增
    cleardisplayPrintHistory,
    printCoverFile,
    filterFilesForPrint,
    $reset,
    getMqttStatus,
    reconnectMqtt,
    publishPrintStatus,
    findAvailablePrintersForOrder,
    executeSmartPrint
  };
}, {
  // 添加 persist 配置
  persist: {
    key: 'print-store',
    storage: localStorage,
    paths: ['globalPrintSettings', 'printerCapabilities']
  }
});

















