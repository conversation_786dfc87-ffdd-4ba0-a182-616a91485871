// src/api/printer.ts

import { requestClient } from '#/api/request';

import type {
  PrinterApi
} from '#/types/print.types';


/**
 * 获取待打印订单文件列表
 * @param params 查询参数
 * @param params.queryType 查询类型：1-待打印订单，2-历史订单
 * @param params.startDate 开始日期（历史订单搜索用）
 * @param params.endDate 结束日期（历史订单搜索用）
 * @param params.status 订单状态（历史订单搜索用）
 * @param params.bussId 订单号（历史订单搜索用）
 */
export async function getReadyPrintFiles(params: PrinterApi.GetReadyPrintFilesParams) {
  // 处理日期参数格式化
  const processedParams = { ...params };

  // 如果是历史订单查询且有日期参数，格式化日期
  if (params.queryType === 2) {
    if (params.startDate) {
      processedParams.startDate = typeof params.startDate === 'string'
        ? params.startDate
        : params.startDate.toISOString().split('T')[0];
    }
    if (params.endDate) {
      processedParams.endDate = typeof params.endDate === 'string'
        ? params.endDate
        : params.endDate.toISOString().split('T')[0];
    }
  }

  return requestClient.get('/print/order/file/query/ready/list', {
    params: processedParams,
  });
}

/**
 * 获取店铺配置得打印机
 */
export async function getShopPrinter(params: PrinterApi.getShopPrinterParams) {
  const { shopId } = params;
  return requestClient.get(`/shop/printer/list/${shopId}`);
}


/**
 * 打印订单认领
 */
export async function orderConfirm(params) {
  const { bussId,employeeId, confirmType } = params;
  return requestClient.post(`/mqtt/print/order/bussId/${bussId}/confirm`,{
    employeeId,
    confirmType
  });
}


/**
 * 开始打印订单
 */
export async function printOrderStart(params) {
  const { bussId } = params;
  return requestClient.post(`/mqtt/print/order/bussId/${bussId}/start`);
}


/**
 * 打印结果通知
 */
export async function printFileReport(params) {
  const { printFileId, printResult , printResultDesc , printResultTime } = params;
  return requestClient.post(`/mqtt/print/file/printFileId/${printFileId}/report`, {
    printResult,
    printResultDesc,
    printResultTime,
  });
}

/**
 * 添加打印机
 */
export async function shopAddPrinter(params) {

  return requestClient.post(`/shop/printer/add`, {
    params: params,
  });
}

/**
 * 修改打印机
 */
export async function shopUpPrinter(params) {
  return requestClient.post(`/shop/printer/update`, {
    params: params,
  });
}

/**
 * 删除打印机
 */
export async function shopDelPrinter(params) {
  const { printerId } = params;
  return requestClient.delete(`/shop/printer/delete/${printerId}`);
}

/**
 * 设置打印机
 */
export async function setPrinterSettings(params: PrinterApi.SetPrinterSettingsParams) {
  const { printerId, params: settingParams } = params;
  return requestClient.post(`/print/printer/${printerId}/setting`, {
    params: settingParams,
  });
}

/**
 * 更新打印机状态
 */
export async function updatePrinterStatus(params: PrinterApi.UpdatePrinterStatusParams) {
  const { printerId, status } = params;
  return requestClient.post(`/print/printer/${printerId}/status`, {
    status,
  });
}

/**
 * 拒绝订单
 */
export async function rejectOrder(params: { bussId: string; reason: string }) {
  const { bussId, reason } = params;
  return requestClient.post('/print/order/reject', {
    bussId,
    reason
  });
}


