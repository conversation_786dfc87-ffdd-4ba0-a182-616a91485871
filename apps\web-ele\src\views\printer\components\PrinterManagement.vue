<template>
  <div class="printer-management-page">
    <div class="page-header">
      <el-tabs v-model="activeTab" class="management-tabs">
        <el-tab-pane label="打印机管理" name="management"></el-tab-pane>
        <el-tab-pane label="打印设置" name="settings"></el-tab-pane>
        <!-- <el-tab-pane label="分流设置" name="shunting"></el-tab-pane> -->
      </el-tabs>
    </div>

    <!-- 打印机管理标签页 -->
    <div v-if="activeTab === 'management'" class="main-content">
      <div class="printer-list">
        <el-card v-for="config in printStore.printerCapabilities" :key="config.id" class="printer-card" shadow="never">
          <div class="card-body">
            <div class="icon-wrapper"><el-icon><Printer /></el-icon></div>
            <div class="info-wrapper">
              <div class="info-header" style="display: flex; flex-direction: column; align-items: flex-start;">
                <span class="printer-name">
                  {{ config.alias }}
                  <el-button link type="warning" @click="handleEdit(config)">编辑</el-button>
                  <el-button link type="danger" @click="handleDelete(config.id)">删除</el-button>
                </span>
                <ElTag size="small" type="info">{{ config.printerName }}</ElTag>
              </div>
              <div class="info-line tags-line">
                <ElTag v-for="size in config.paperSizes" :key="size" type="info" size="small">{{ size }}</ElTag>

                <!-- 修复颜色模式显示 -->
                <ElTag v-for="color in config.colorModes" :key="color" type="warning" size="small" plain>
                  {{ color === 'COLOR' ? '彩色' : '黑白' }}
                </ElTag>

                <!-- 修复双面模式显示 -->
                <ElTag v-for="duplex in config.duplexModes" :key="duplex" type="success" size="small" plain>
                  {{ duplex === 'DOUBLE' ? '双面' : '单面' }}
                </ElTag>

                <ElTag v-if="config.status == 'enabled'" type="success" size="small" plain>启用</ElTag>
                <ElTag v-else type="danger" size="small" plain>禁用</ElTag>
              </div>
            </div>
          </div>
        </el-card>
        <el-empty v-if="printStore.printerCapabilities.length === 0" description="暂无打印机配置" />
      </div>

    </div>

    <!-- 打印设置标签页 -->
    <div v-if="activeTab === 'settings'" class="main-content">
      <div class="settings-content">
            <div class="settings-form">
            <SettingsForm />
          </div>
      </div>
    </div>

    <div class="page-footer">
      <el-button type="primary" size="large" @click="handleOpenAddDialog();">添加打印机</el-button>
      <!-- <el-button type="danger" size="large" @click="deletePrinter">删除打印机</el-button> -->
    </div>
    <div class="log-section">
      <div class="log-panel">
        <div class="log-header">
          <span>打印日志</span>
          <div class="log-actions">
            <el-button
              v-if="printStore.printHistory.length > 0"
              type="text"
              size="small"
              @click="printStore.clearPrintHistory()"
            >
              清空历史
            </el-button>
            <el-button
              v-if="printStore.displayPrintLogs.length > 0"
              type="text"
              size="small"
              @click="printStore.cleardisplayPrintHistory()"
            >
              删除任务
            </el-button>
          </div>
        </div>

        <div class="log-content">
          <el-empty v-if="printStore.displayPrintLogs.length === 0" description="暂无打印任务" :image-size="50" />
          <div v-for="job in printStore.displayPrintLogs" :key="job.jobKey" class="log-entry">
            <div class="job-info">
              <el-icon class="job-icon" :class="`status-${job.status}`">
                <SuccessFilled v-if="job.status === 'completed'" />
                <CloseBold v-else-if="job.status === 'failed'" />
                <Loading v-else />
              </el-icon>
              <div class="job-details">
                <span class="file-name" :title="job.file.fileName">{{ job.file.fileName }}</span>
                <span class="printer-name">-> {{ job.printerName }}</span>
                <span v-if="job.status === 'failed' && job.errorMessage" class="error-message">
                  {{ job.errorMessage }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>



    <Modal  :title="modalTitle" :showCancelButton="false" :showConfirmButton="false">
      <Form />
    </Modal>
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits , onMounted, computed, watch, nextTick } from 'vue';
import type { PropType } from 'vue';
import { ElTabs, ElTabPane, ElCard, ElIcon, ElTag, ElButton , ElMessage, ElEmpty } from 'element-plus';
import { Printer, SuccessFilled, CloseBold, Loading } from '@element-plus/icons-vue';


import { useVbenForm } from '#/adapter/form';
import { useVbenModal, VbenButton } from '@vben/common-ui';

const [Modal, modalApi] = useVbenModal();

import type { FormSchema } from '@vben/common-ui';

// 引入类型、Store 和 aardio 接口
import type { Printer as PrinterType, PrinterCapability, PrintJob, PrintStatusData } from '#/types/print.types';
import { usePrintStore } from '#/stores/printStore';
import { aardio } from '#/aardio/index';

const printStore = usePrintStore();

// --- 新增: 为修复代码完整性，添加 printJobs 和 logs 的定义 ---
const logs = ref<{message: string, timestamp: string}[]>([]); // 日志数组
const printJobs = ref<PrintJob[]>([]); // 假设的打印任务队列

defineProps({
 printers: { type: Array as PropType<any[]>, required: true },
 activePrinter: { type: Object as PropType<PrinterType | null>, default: null }
});

const emit = defineEmits(['toggle-printer', 'delete-printer']);

const printerOptions = ref([]);
const mode = ref<'add' | 'edit'>('add');

const editingId = ref<string | null>(null);
const isDialogVisible = ref(false);
// --- 本地状态 ---
const activeTab = ref('management');
const modalTitle = ref('新增打印机配置');
const settingsMode = ref(false); // 是否为设置模式

// 2.使用 useVbenForm Hook - 重新定义表单结构
const [Form, formApi] = useVbenForm({
   commonConfig: {
    componentProps: { class: 'w-full' },
   },
   layout: 'horizontal',
   handleSubmit: (values) => {
    handleSubmit(values);
   },
   schema: [
    { component: 'Input', fieldName: 'alias', label: '别名' },
    {
      component: 'Select',
      fieldName: 'printerName',
      label: '选择打印机',
      required: true,
      componentProps: {
        placeholder: '正在获取物理打印机...',
        options: printerOptions
      }
    },
    {
      component: 'CheckboxGroup',
      fieldName: 'paperSizes',
      label: '支持纸张',
      required: true,
      componentProps: {
        options: [
          { label: 'A3', value: 'A3' },
          { label: 'A4', value: 'A4' },
          { label: 'B4', value: 'B4' },
          { label: 'B5', value: 'B5' }
        ]
      },
      dependencies: {
        trigger(values, form) {
          console.log('纸张选择变化触发:', values.paperSizes);
          updatePaperBinFields(values.paperSizes || []); // 添加模式不传现有值
        },
        triggerFields: ['paperSizes'],
      }
    },
    {
      component: 'CheckboxGroup',
      fieldName: 'duplexModes',
      label: '单双面',
      required: true,
      componentProps: {
        options: [
          { label: '单面', value: 'SINGLE' },
          { label: '双面', value: 'DOUBLE' }
        ]
      }
    },
    {
      component: 'CheckboxGroup',
      fieldName: 'colorModes',
      label: '色彩类型',
      required: true,
      componentProps: {
        options: [
          { label: '黑白', value: 'BLACKWHITE' },
          { label: '彩印', value: 'COLOR' }
        ]
      }
    },
    {
      component: 'CheckboxGroup',
      fieldName: 'layouts',
      label: '支持版式',
      required: true,
      componentProps: {
        options: [
          { label: '竖版', value: 'VERTICAL' },
          { label: '横板', value: 'HORIZONTAL' }
        ]
      }
    },
    {
      component: 'CheckboxGroup',
      fieldName: 'supportedTypes',
      label: '打印类型',
      required: true,
      componentProps: {
        options: [
          { label: '文档', value: '1' },
          { label: '封面', value: '2' },
          { label: '照片', value: '3' } ,
          { label: '订单页', value: '4' }
        ]
      },
      dependencies: {
        trigger(values, form) {
          console.log('打印类型变化触发:', values.supportedTypes);
          updateOrderPageBinField(values.supportedTypes || []); // 添加模式不传现有值
        },
        triggerFields: ['supportedTypes'],
      }
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '状态',
      required: true,
      componentProps: {
        options: [
          { label: '启用', value: 'enabled' },
          { label: '禁用', value: 'disabled' }
        ]
      }
    }
   ]
});

// 动态更新纸盒配置字段 - 修复数据结构
function updatePaperBinFields(selectedSizes: string[], existingValues?: Record<string, string>) {
  console.log('开始更新纸盒字段，选中的纸张:', selectedSizes, '现有值:', existingValues);

  formApi.setState((prev) => {
    // 获取当前schema，移除所有纸盒字段
    const currentSchema = prev.schema || [];
    const baseSchema = currentSchema.filter(item =>
      !item.fieldName?.startsWith('paperBins.')
    );

    // 为每个选中的纸张创建对应的纸盒字段
    const paperBinFields = selectedSizes.map(size => {
      const fieldName = `paperBins.${size}`;
      const existingValue = existingValues?.[size];

      return {
        component: 'Input',
        fieldName,
        label: `${size}纸盒`,
        componentProps: {
          placeholder: 'auto',
          clearable: true
        }
      };
    });

    // 找到合适的插入位置
    const orderPageBinIndex = baseSchema.findIndex(item => item.fieldName === 'orderPageBin');
    const statusIndex = baseSchema.findIndex(item => item.fieldName === 'status');

    let insertIndex = statusIndex !== -1 ? statusIndex : baseSchema.length;
    if (orderPageBinIndex !== -1 && orderPageBinIndex < insertIndex) {
      insertIndex = orderPageBinIndex;
    }

    // 插入纸盒字段
    const newSchema = [
      ...baseSchema.slice(0, insertIndex),
      ...paperBinFields,
      ...baseSchema.slice(insertIndex)
    ];

    return { schema: newSchema };
  });

  // 设置纸盒字段的值
  if (existingValues) {
    nextTick(() => {
      selectedSizes.forEach(size => {
        const fieldName = `paperBins.${size}`;
        const value = existingValues[size] || 'auto';
        console.log(`设置纸盒字段值: ${fieldName} = ${value}`);
        formApi.setFieldValue(fieldName, value);
      });
    });
  }
}

// 动态更新订单页纸盒字段 - 同样使用defaultValue
function updateOrderPageBinField(selectedTypes: string[], existingValue?: string) {
  formApi.setState((prev) => {
    // 移除现有的订单页纸盒字段，但保留状态字段
    let schema = prev.schema?.filter(item =>
      item.fieldName !== 'orderPageBin'
    ) || [];

    // 如果支持订单页打印，添加订单页纸盒字段
    if (selectedTypes.includes('4')) {
      const orderPageBinField = {
        component: 'Input',
        fieldName: 'orderPageBin',
        label: '订单页纸盒',
        defaultValue: existingValue || 'auto', // 直接设置defaultValue
        componentProps: {
          placeholder: 'auto',
          clearable: true
        }
      };

      // 在状态字段前插入
      const statusIndex = schema.findIndex(item => item.fieldName === 'status');
      if (statusIndex !== -1) {
        schema.splice(statusIndex, 0, orderPageBinField);
      } else {
        schema.push(orderPageBinField);
      }
    }

    console.log('更新订单页纸盒字段，schema长度:', schema.length);
    return { schema };
  });
}

// 监听表单值变化，强制重新渲染schema
// watch(() => formApi.form?.values, (newValues) => {
//   console.log('表单值变化:', newValues);
//   // 触发schema重新计算
//   nextTick(() => {
//     formApi.form?.validate?.();
//   });
// }, { deep: true });

watch(activeTab, (newTab) => {
  if (newTab === 'settings') {
    // 切换到设置页时，重新加载设置数据
    nextTick(() => {
      const currentSettings = printStore.globalPrintSettings;
      console.log('重新加载设置数据:', currentSettings);
      settingsFormApi.setValues(currentSettings);
    });
  }
}, { immediate: false });

// 全局设置表单
const [SettingsForm, settingsFormApi] = useVbenForm({
   commonConfig: {
    componentProps: { class: 'w-full' },
   },
   layout: 'horizontal',
   handleSubmit: (values) => {
    handleSettingsSubmit(values);
   },
   schema: [
     { component: 'Select', fieldName: 'mode', label: '手动/自动', componentProps: { options: [{ label: '自动打印', value: 'auto' }, { label: '手动打印', value: 'manual' }] } },
     { component: 'Select', fieldName: 'sound', label: '播放设置', componentProps: { options: [{ label: '播报', value: 'play' }, { label: '不播报', value: 'mute' }] } },
     { component: 'Select', fieldName: 'orderPageMode', label: '订单页', componentProps: { options: [{ label: '按份数', value: 'byCopies' }, { label: '按打印机', value: 'byPrinter' }] } },
   ]
});

// 处理全局设置提交
async function handleSettingsSubmit(values) {
  try {
    await settingsFormApi.validate();
    printStore.saveGlobalPrintSettings(values);
    ElMessage.success('全局打印设置保存成功！');
  } catch (error) {
    console.error("设置保存失败", error);
  }
}

// 点击打印机列表项时，调用 store 的 action
const handlePrinterSelect = (printer: PrinterType) => {
 printStore.selectPrinter(printer);
};

// --- 事件处理 ---
function handleOpenAddDialog() {
  mode.value = 'add';
  settingsMode.value = false;
  editingId.value = null;

  // 重置表单到初始状态
  formApi.resetForm();

  // 使用可复用的schema构建函数
  const baseSchema = createPrinterFormSchema();
  formApi.setState({ schema: baseSchema });

  // 延迟设置默认值
  setTimeout(() => {
    formApi.setValues({
      status: 'enabled'
    });
  }, 50);

  modalTitle.value = '添加打印机';
  modalApi.open();
}

// 处理编辑时的数据回显 - 修复显示问题
function handleEdit(config: PrinterCapability) {
  console.log('=== 开始处理编辑 ===');
  console.log('原始配置数据:', JSON.stringify(config, null, 2));

  mode.value = 'edit';
  settingsMode.value = false;
  editingId.value = config.id;
  modalTitle.value = '修改打印机';

  // 先打开模态框
  modalApi.open();

  // 等待模态框完全打开后再处理表单
  nextTick(() => {
    // 重置表单
    formApi.resetForm();

    // 构建表单数据
    const formData = {
      alias: config.alias || '',
      printerName: config.printerName || '',
      paperSizes: config.paperSizes || [],
      duplexModes: config.duplexModes || [],
      colorModes: config.colorModes || [],
      layouts: config.layouts || [],
      supportedTypes: config.supportedTypes || [],
      status: config.status || 'enabled',
      paperBins: config.paperBins || {},
      orderPageBin: config.orderPageBin || 'auto'
    };

    console.log('构建的表单数据:', formData);

    // schema构建函数
    let finalSchema = createPrinterFormSchema();

    // 添加纸盒字段
    finalSchema = addPaperBinFieldsToSchema(finalSchema, config.paperSizes || [], config.paperBins);

    // 添加订单页纸盒字段
    finalSchema = addOrderPageBinFieldToSchema(finalSchema, config.supportedTypes || []);

    console.log('最终schema字段:', finalSchema.map(s => s.fieldName));

    // 设置schema
    formApi.setState({ schema: finalSchema });

    // 等待schema设置完成后再设置值
    nextTick(() => {
      console.log('设置表单值:', formData);
      formApi.setValues(formData);
    });
  });
}

function handleDelete(configId: string) {
 printStore.deletePrinterCapability(configId);
}

// 处理表单提交 - 添加更详细的调试信息
async function handleSubmit(values) {
  try {
    console.log('=== 开始处理表单提交 ===');
    console.log('原始表单数据:', JSON.stringify(values, null, 2));

    await formApi.validate();

    // 构建最终配置对象
    const finalConfig = {
      alias: values.alias || '',
      printerName: values.printerName,
      paperSizes: values.paperSizes || [],
      duplexModes: values.duplexModes || [],
      colorModes: values.colorModes || [],
      layouts: values.layouts || [],
      supportedTypes: values.supportedTypes || [],
      status: values.status,
      paperBins: {},
      orderPageBin: undefined
    };

    // 处理纸盒配置
    console.log('开始处理纸盒配置...');
    if (Array.isArray(finalConfig.paperSizes) && finalConfig.paperSizes.length > 0) {
      finalConfig.paperSizes.forEach(size => {
        const binFieldName = `paperBins.${size}`;
        const binValue = values['paperBins'][size];
        console.log(`处理纸盒 ${size}: 字段名=${binFieldName}, 值=${binValue}`);

        if (binValue !== undefined && binValue !== null && binValue !== '') {
          finalConfig.paperBins[size] = String(binValue).trim();
        } else {
          finalConfig.paperBins[size] = 'auto';
        }
      });
    }

    // 处理订单页纸盒
    if (finalConfig.supportedTypes.includes('4')) {
      const orderPageBinValue = values.orderPageBin;
      console.log('处理订单页纸盒:', orderPageBinValue);
      finalConfig.orderPageBin = orderPageBinValue && String(orderPageBinValue).trim()
        ? String(orderPageBinValue).trim()
        : 'auto';
    } else {
      delete finalConfig.orderPageBin;
    }

    console.log('最终配置对象:', JSON.stringify(finalConfig, null, 2));

    // 提交数据
    let success = false;
    if (mode.value === 'add') {
      success = printStore.addPrinterCapability(finalConfig);
    } else {
      const updatedConfig = { id: editingId.value!, ...finalConfig };
      success = printStore.updatePrinterCapability(updatedConfig);
    }

    if (success) {
      ElMessage.success(mode.value === 'add' ? '打印机添加成功！' : '打印机更新成功！');
      modalApi.close();
      formApi.resetForm();
    } else {
      ElMessage.error('操作失败，请重试');
    }
  } catch (error) {
    console.error('表单提交失败:', error);
    ElMessage.error('表单验证失败，请检查输入');
  }
}

// [新增] 组件挂载时，从 aardio 获取物理打印机列表并更新到表单
onMounted(async () => {
 try {
  const physicalPrinters = await aardio.getPrinters();
  console.log("获取到的物理打印机列表:", physicalPrinters);
  printerOptions.value = physicalPrinters.map(p => ({
   label: p.label,
   value: p.label
  }));

  // 加载全局设置到表单
  settingsFormApi.setValues(printStore.globalPrintSettings);
 } catch (error) {
  ElMessage.error("获取物理打印机列表失败！");
  console.error(error);
 }
});

// --- 方法 ---
const getStatusInfo = (status: Printer['status']) => {
 switch (status) {
  case 'idle':
   return { text: '空闲', actionText: '禁用打印机' };
  case 'disabled':
   return { text: '禁用', actionText: '启用打印机' };
  case 'printing':
   return { text: '打印中', actionText: '禁用打印机' };
  default:
   return { text: '未知', actionText: 'N/A' };
 }
};

const togglePrinterStatus = (printer: Printer) => emit('toggle-printer', printer);
const addPrinter = () => {
 isDialogVisible.value = true;
};
const deletePrinter = () => emit('delete-printer');

// 重试失败的打印任务
async function retryFailedJob(job: PrintJob) {
  try {
    // 找到对应的打印机配置
    const printerConfig = printStore.printerCapabilities.find(
      p => p.printerName === job.printerName
    );

    if (!printerConfig) {
      ElMessage.error(`找不到打印机配置: ${job.printerName}`);
      return;
    }

    // 重新执行打印任务
    await printStore.executePrintJob(job.file, printerConfig, job.file.copies);
    ElMessage.success(`已重新提交打印任务: ${job.file.fileName}`);
  } catch (error) {
    console.error('重试打印任务失败:', error);
    ElMessage.error('重试打印任务失败');
  }
}

// 创建可复用的schema构建函数
function createPrinterFormSchema() {
  return [
    { component: 'Input', fieldName: 'alias', label: '别名' },
    {
      component: 'Select',
      fieldName: 'printerName',
      label: '选择打印机',
      required: true,
      componentProps: {
        placeholder: '正在获取物理打印机...',
        options: printerOptions.value
      }
    },
    {
      component: 'CheckboxGroup',
      fieldName: 'paperSizes',
      label: '支持纸张',
      required: true,
      componentProps: {
        options: [
          { label: 'A3', value: 'A3' },
          { label: 'A4', value: 'A4' },
          { label: 'B4', value: 'B4' },
          { label: 'B5', value: 'B5' }
        ]
      },
      dependencies: {
        trigger(values, form) {
          console.log('纸张选择变化:', values.paperSizes);
          const existingBins = values.paperBins || {};
          updatePaperBinFields(values.paperSizes || [], existingBins);
        },
        triggerFields: ['paperSizes'],
      }
    },
    {
      component: 'CheckboxGroup',
      fieldName: 'duplexModes',
      label: '双面打印',
      required: true,
      componentProps: {
        options: [
          { label: '单面', value: 'SINGLE' },
          { label: '双面', value: 'DOUBLE' }
        ]
      }
    },
    {
      component: 'CheckboxGroup',
      fieldName: 'colorModes',
      label: '颜色模式',
      required: true,
      componentProps: {
        options: [
          { label: '黑白', value: 'BLACKWHITE' },
          { label: '彩色', value: 'COLOR' }
        ]
      }
    },
    {
      component: 'CheckboxGroup',
      fieldName: 'layouts',
      label: '布局方向',
      required: true,
      componentProps: {
        options: [
          { label: '纵向', value: 'VERTICAL' },
          { label: '横向', value: 'HORIZONTAL' }
        ]
      }
    },
    {
      component: 'CheckboxGroup',
      fieldName: 'supportedTypes',
      label: '支持类型',
      required: true,
      componentProps: {
        options: [
          { label: '文档', value: '1' },
          { label: '图片', value: '2' },
          { label: '照片', value: '3' },
          { label: '订单页', value: '4' }
        ]
      },
      dependencies: {
        trigger(values, form) {
          console.log('支持类型变化:', values.supportedTypes);
          const orderPageBin = values.orderPageBin || 'auto';
          updateOrderPageBinField(values.supportedTypes || [], orderPageBin);
        },
        triggerFields: ['supportedTypes'],
      }
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '状态',
      required: true,
      componentProps: {
        options: [
          { label: '启用', value: 'enabled' },
          { label: '禁用', value: 'disabled' }
        ]
      }
    }
  ];
}

// 添加纸盒字段到schema
function addPaperBinFieldsToSchema(baseSchema: any[], paperSizes: string[], paperBins: Record<string, string>) {
  if (!paperSizes || paperSizes.length === 0) return baseSchema;

  const statusIndex = baseSchema.findIndex(item => item.fieldName === 'status');
  const insertIndex = statusIndex !== -1 ? statusIndex : baseSchema.length;

  const paperBinFields = paperSizes.map(size => ({
    component: 'Input',
    fieldName: `paperBins.${size}`,
    label: `${size}纸盒`,
    componentProps: {
      placeholder: 'auto',
      clearable: true
    }
  }));

  return [
    ...baseSchema.slice(0, insertIndex),
    ...paperBinFields,
    ...baseSchema.slice(insertIndex)
  ];
}

// 添加订单页纸盒字段到schema
function addOrderPageBinFieldToSchema(baseSchema: any[], supportedTypes: string[]) {
  if (!supportedTypes || !supportedTypes.includes('4')) return baseSchema;

  const statusIndex = baseSchema.findIndex(item => item.fieldName === 'status');
  const insertIndex = statusIndex !== -1 ? statusIndex : baseSchema.length;

  const orderPageBinField = {
    component: 'Input',
    fieldName: 'orderPageBin',
    label: '订单页纸盒',
    componentProps: {
      placeholder: 'auto',
      clearable: true
    }
  };

  return [
    ...baseSchema.slice(0, insertIndex),
    orderPageBinField,
    ...baseSchema.slice(insertIndex)
  ];
}
</script>

<style scoped lang="scss">
.printer-management-page {
  display: flex;
  flex-direction: column;
  height: 93vh; /* 固定页面高度为视口高度 */
  background-color: #1a202c;
  color: #e2e8f0;
}

.page-header {
  flex-shrink: 0; /* 头部不压缩 */
  padding: 0 16px;
}

/* Tab 样式重写 */
:deep(.management-tabs) {
  .el-tabs__header { margin: 0; }
  .el-tabs__nav-wrap::after { background-color: transparent; }
  .el-tabs__item {
    color: #a0aec0;
    &.is-active { color: #409EFF; }
  }
  .el-tabs__active-bar { background-color: #409EFF; }
}

/* 中间主内容区，可滚动 */
.main-content {
  flex: 1; /* 占据所有剩余空间 */
  min-height: 0; /* Flexbox 滚动区域技巧 */
  overflow-y: auto; /* 内容溢出时显示垂直滚动条 */
  padding: 16px;
  height: 50vh;
}

/* 打印机列表 */
.printer-list {
  background-color: #1f2937;

  display: grid;
  gap: 16px;
}



.printer-card {

  background-color: #2d3748;
  /* 调整边框颜色和圆角以匹配效果图 */
  border: 1px solid #4a5568;
  border-radius: 6px;
  transition: all 0.2s ease-in-out;

  &:hover {
    border-color: #636d81;
  }

  .card-body {
    display: flex;
    align-items: center;
    gap: 16px;
  }
}

.icon-wrapper {
  background-color: #409eff;
  color: #fff;
  width: 50px;
  height: 50px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  flex-shrink: 0;
}

.info-wrapper {
  flex-grow: 1;
  .info-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
  }
  .printer-name {
    font-size: 14px;
    font-weight: 600;
    color: #fff;
  }
  .status-badge {
    margin-left: 12px;
    padding: 3px 8px;
    font-size: 12px;
    border-radius: 4px;
    color: #fff;
    /* 精确设置状态徽章的背景色 */
    &.status-disabled { background-color: #d94848; }
    &.status-idle { background-color: #18a058; }
    &.status-printing { background-color: #f0a020; }
  }
  .info-line {
    font-size: 13px;
    color: #a0aec0;
  }
  .tags-line {
    margin-top: 8px;
    display: flex;
    gap: 8px;
    :deep(.ElTag) {
      /* 精确设置标签的样式 */
      background-color: #3f4a5f;
      border-color: transparent;
      color: #c1c8d4;
    }
  }
}

.action-wrapper {
  flex-shrink: 0;

  /* 精确设置 plain 按钮的样式 */
  :deep(.el-button.is-plain) {
    --el-button-disabled-text-color: #777;
    --el-button-disabled-bg-color: #2d3748;
    --el-button-disabled-border-color: #4a5568;
  }
  :deep(.el-button--primary.is-plain) {
    color: #70b5fb;
    border-color: #385a84;
    background-color: #2c3a5e;
    &:hover, &:focus {
      color: #fff;
      border-color: #409EFF;
      background-color: #409EFF;
    }
  }
  :deep(.el-button--danger.is-plain) {
    color: #f89898;
    border-color: #6b3d3d;
    background-color: #4d2f2f;
     &:hover, &:focus {
      color: #fff;
      border-color: #f56c6c;
      background-color: #f56c6c;
    }
  }
}


/* 页面底部操作栏 */
.page-footer {
  flex-shrink: 0;
  display: flex;
  gap: 16px;
  padding: 16px 0 0 0;
  .el-button {
    flex: 1;
  }
}

/* 底部日志区域 */
.log-section {
  flex-shrink: 0; /* 日志区域不压缩 */
  height: 35vh; /* 固定高度 */
  background-color: #2d3748;
  border-top: 1px solid #4a5568;
  display: flex;
  flex-direction: column;
}

.log-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 16px;
  min-height: 0; /* 重要：允许flex子项收缩 */

  .log-header {
    flex-shrink: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #4a5568;

    span {
      font-weight: 600;
      color: #e2e8f0;
    }
  }

  .log-content {
    flex: 1;
    overflow-y: auto;
    min-height: 0;

    /* 美化滚动条 */
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #1a202c;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #4a5568;
      border-radius: 3px;

      &:hover {
        background: #6b7280;
      }
    }
  }

  .log-entry {
    padding: 8px 0;
    border-bottom: 1px solid #374151;
    &:last-child {
      border-bottom: none;
    }
  }

  .job-info {
    display: flex;
    align-items: center;
  }

  .job-icon {
    margin-right: 8px;
    font-size: 16px;
    &.status-completed { color: var(--el-color-success); }
    &.status-failed { color: var(--el-color-danger); }
    &.status-queuing { color: var(--el-color-primary); }
  }

  .job-details {
    display: flex;
    flex-direction: column;
    font-size: 12px;
    line-height: 1.4;
  }

  .file-name {
    color: #e2e8f0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 180px;
  }

  .printer-name {
    color: #a0aec0;
  }

  .error-message {
    color: var(--el-color-danger);
    font-size: 11px;
    margin-top: 4px;
    padding-left: 24px;
  }
}

/* 美化所有滚动条 */
.main-content::-webkit-scrollbar,
.log-viewer::-webkit-scrollbar {
  width: 8px;
}
.main-content::-webkit-scrollbar-track,
.log-viewer::-webkit-scrollbar-track {
  background: #1a202c;
}
.main-content::-webkit-scrollbar-thumb,
.log-viewer::-webkit-scrollbar-thumb {
  background: #4a5568;
  border-radius: 4px;
  &:hover { background: #a0aec0; }
}

/* 打印设置标签页 */
.settings-content {
  display: grid;
  gap: 16px;
}

.printer-settings-card {
  background-color: #2d3748;
  border: 1px solid #4a5568;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .settings-info {
    p {
      margin: 8px 0;
      color: #e2e8f0;
    }
  }
}

</style>




















